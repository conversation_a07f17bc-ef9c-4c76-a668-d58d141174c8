extends Node2D

# 建筑基类
class_name Building

# 建筑属性
var building_type: String
var building_data: Dictionary
var position_on_grid: Vector2
var production_timer: float = 0.0
var level: int = 1

# 引用游戏管理器
var game_manager: GameManager

func _ready():
	# 获取游戏管理器引用
	game_manager = get_node("/root/GameManager")

func initialize(type: String, data: Dictionary, pos: Vector2):
	building_type = type
	building_data = data
	position_on_grid = pos
	position = pos
	
	# 设置建筑外观（简单的彩色方块）
	setup_visual()
	
	print("建筑初始化完成：%s 在 %s" % [building_data.name, pos])

func setup_visual():
	# 创建简单的视觉表示
	var sprite = Sprite2D.new()
	add_child(sprite)
	
	# 创建简单的彩色纹理
	var texture = ImageTexture.new()
	var image = Image.create(64, 64, false, Image.FORMAT_RGB8)
	
	# 根据建筑类型设置颜色
	var color = Color.WHITE
	match building_type:
		"house":
			color = Color.BLUE
		"generator":
			color = Color.YELLOW
		"shop":
			color = Color.GREEN
	
	image.fill(color)
	texture.set_image(image)
	sprite.texture = texture
	
	# 添加建筑名称标签
	var label = Label.new()
	label.text = building_data.name
	label.position = Vector2(-32, -40)
	label.add_theme_color_override("font_color", Color.BLACK)
	add_child(label)

func update_production():
	# 更新生产计时器
	production_timer += 1.0
	
	# 检查是否到达生产时间
	if production_timer >= building_data.production_rate:
		produce_resources()
		production_timer = 0.0

func produce_resources():
	# 生产资源
	if "production" in building_data:
		for resource_type in building_data.production:
			var amount = building_data.production[resource_type] * level
			game_manager.add_resource(resource_type, amount)

func upgrade():
	# 升级建筑
	var upgrade_cost = calculate_upgrade_cost()
	
	if game_manager.can_afford(upgrade_cost):
		if game_manager.spend_resources(upgrade_cost):
			level += 1
			print("%s 升级到等级 %d" % [building_data.name, level])
			update_visual_for_level()
			return true
	
	print("资源不足，无法升级 %s" % building_data.name)
	return false

func calculate_upgrade_cost() -> Dictionary:
	# 计算升级成本（随等级递增）
	var base_cost = building_data.cost
	var upgrade_cost = {}
	
	for resource_type in base_cost:
		upgrade_cost[resource_type] = base_cost[resource_type] * level * 2
	
	return upgrade_cost

func update_visual_for_level():
	# 更新建筑外观以反映等级
	var label = get_child(1) as Label  # 假设标签是第二个子节点
	if label:
		label.text = "%s (Lv.%d)" % [building_data.name, level]

func get_info() -> Dictionary:
	# 返回建筑信息
	return {
		"type": building_type,
		"name": building_data.name,
		"level": level,
		"position": position_on_grid,
		"production": building_data.production,
		"production_rate": building_data.production_rate
	}

# 点击事件处理
func _input_event(viewport, event, shape_idx):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		on_building_clicked()

func on_building_clicked():
	print("点击了建筑：%s (等级 %d)" % [building_data.name, level])
	# 这里可以显示建筑信息或升级选项
	show_building_info()

func show_building_info():
	# 显示建筑信息（后续可以创建UI面板）
	var info = get_info()
	print("建筑信息：", info)
