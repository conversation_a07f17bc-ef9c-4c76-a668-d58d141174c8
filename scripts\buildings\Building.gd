extends Node2D

# 建筑基类
class_name Building

# 建筑属性
var building_type: String
var building_data: Dictionary
var position_on_grid: Vector2
var production_timer: float = 0.0
var level: int = 1

# 引用游戏管理器
var game_manager: GameManager

func _ready():
	# GameManager引用将在initialize中设置
	pass

func initialize(type: String, data: Dictionary, pos: Vector2, manager: GameManager = null):
	building_type = type
	building_data = data
	position_on_grid = pos
	position = pos

	# 设置游戏管理器引用
	if manager:
		game_manager = manager
	else:
		# 尝试自动查找GameManager
		if has_node("../GameManager"):
			game_manager = get_node("../GameManager")
		elif get_parent() and get_parent().has_node("GameManager"):
			game_manager = get_parent().get_node("GameManager")
		else:
			print("警告：无法找到GameManager，建筑功能可能无法正常工作")

	# 设置建筑外观
	setup_visual()

	print("建筑初始化完成：%s 在 %s" % [building_data.name, pos])

func setup_visual():
	# 纯文字建筑显示
	var building_icon = get_building_icon()

	# 创建建筑图标标签
	var icon_label = Label.new()
	icon_label.text = building_icon
	icon_label.position = Vector2(-16, -32)
	icon_label.add_theme_font_size_override("font_size", 32)
	add_child(icon_label)

	# 添加建筑名称标签
	var name_label = Label.new()
	name_label.text = building_data.name
	name_label.position = Vector2(-40, 5)
	name_label.add_theme_font_size_override("font_size", 12)
	name_label.add_theme_color_override("font_color", Color.WHITE)
	name_label.add_theme_color_override("font_shadow_color", Color.BLACK)
	name_label.add_theme_constant_override("shadow_offset_x", 1)
	name_label.add_theme_constant_override("shadow_offset_y", 1)
	add_child(name_label)

	# 添加等级显示
	var level_label = Label.new()
	level_label.text = "Lv.%d" % level
	level_label.position = Vector2(-15, 20)
	level_label.add_theme_font_size_override("font_size", 10)
	level_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(level_label)

func get_building_icon() -> String:
	# 获取建筑图标
	match building_type:
		"meditation_room": return "🧘"
		"spirit_garden": return "🌿"
		"alchemy_lab": return "⚗️"
		"artifact_forge": return "🔨"
		"disciple_hall": return "🏛️"
		"treasure_vault": return "🏦"
		_: return "🏗️"

func update_production():
	# 更新生产计时器
	production_timer += 1.0

	# 检查是否到达生产时间
	if production_timer >= building_data.production_rate:
		produce_resources()
		production_timer = 0.0

func produce_resources():
	# 生产资源
	if not game_manager:
		print("警告：GameManager未找到，无法生产资源")
		return

	if "production" in building_data:
		for resource_type in building_data.production:
			var amount = building_data.production[resource_type] * level
			game_manager.add_resource(resource_type, amount)

func upgrade():
	# 升级建筑
	if not game_manager:
		print("警告：GameManager未找到，无法升级建筑")
		return false

	var upgrade_cost = calculate_upgrade_cost()

	if game_manager.can_afford(upgrade_cost):
		if game_manager.spend_resources(upgrade_cost):
			level += 1
			print("%s 升级到等级 %d" % [building_data.name, level])
			update_visual_for_level()
			return true

	print("资源不足，无法升级 %s" % building_data.name)
	return false

func calculate_upgrade_cost() -> Dictionary:
	# 计算升级成本（随等级递增）
	var base_cost = building_data.cost
	var upgrade_cost = {}

	for resource_type in base_cost:
		upgrade_cost[resource_type] = base_cost[resource_type] * level * 2

	return upgrade_cost

func update_visual_for_level():
	# 更新建筑外观以反映等级
	var children = get_children()
	if children.size() >= 3:
		var level_label = children[2] as Label  # 等级标签是第三个子节点
		if level_label:
			level_label.text = "Lv.%d" % level

func get_info() -> Dictionary:
	# 返回建筑信息
	return {
		"type": building_type,
		"name": building_data.name,
		"level": level,
		"position": position_on_grid,
		"production": building_data.production,
		"production_rate": building_data.production_rate
	}

# 点击事件处理
func _input_event(_viewport, event, _shape_idx):
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		on_building_clicked()

func on_building_clicked():
	print("点击了建筑：%s (等级 %d)" % [building_data.name, level])
	# 这里可以显示建筑信息或升级选项
	show_building_info()

func show_building_info():
	# 显示建筑信息（后续可以创建UI面板）
	var info = get_info()
	print("建筑信息：", info)
