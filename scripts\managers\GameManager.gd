extends Node

# 修仙游戏管理器 - 负责管理修仙游戏的核心逻辑
class_name GameManager

# 信号
signal resource_changed(resource_type: String, amount: int)
signal cultivation_started(spot_type: String, spot_name: String)
signal cultivation_completed(spot_type: String, rewards: Dictionary)
signal monster_encountered(monster_name: String, location: String, victory: bool)
signal realm_breakthrough(new_realm: String)
signal cultivation_progress(progress: float)

# 修仙资源 - 个人修炼者视角
var resources = {
	"spirit_stones": 10,     # 灵石 - 基础货币（初始很少）
	"qi": 100,              # 灵气 - 修炼根本
	"cultivation_exp": 0,   # 修炼经验
	"sect_contribution": 0, # 门派贡献点
	"pills": 0,             # 丹药
	"artifacts": 0,         # 法器
	"techniques": 0         # 功法数量
}

# 修仙境界系统
var cultivation_realms = {
	# 凡人阶段
	"mortal": {"name": "凡人", "level": 0, "exp_required": 0},
	"beginner": {"name": "初学者", "level": 1, "exp_required": 100},
	"practitioner": {"name": "修行者", "level": 2, "exp_required": 300},

	# 修真境界
	"body_refining": {"name": "炼体期", "level": 3, "exp_required": 600},
	"qi_refining": {"name": "炼气期", "level": 4, "exp_required": 1000},
	"foundation": {"name": "筑基期", "level": 5, "exp_required": 1500},
	"core_formation": {"name": "结丹期", "level": 6, "exp_required": 2500},
	"nascent_soul": {"name": "元婴期", "level": 7, "exp_required": 4000},
	"soul_transformation": {"name": "化神期", "level": 8, "exp_required": 6000},
	"void_integration": {"name": "合道期", "level": 9, "exp_required": 9000},
	"mahayana": {"name": "大乘期", "level": 10, "exp_required": 13000},
	"tribulation": {"name": "渡劫期", "level": 11, "exp_required": 18000}
}

var current_realm = "mortal"
var current_cultivation_exp = 0

# 修炼地点 - 选择地点修炼打怪
var active_cultivation_spots = []  # 当前正在修炼的地点
var cultivation_spots = {
	"meditation_room": {
		"name": "静修室",
		"description": "门派内的安静修炼场所",
		"cost": {"qi": 20},
		"rewards": {"cultivation_exp": 3, "qi": 1},
		"duration": 30.0,  # 修炼持续时间(秒)
		"unlock_realm": "mortal",
		"monsters": [],  # 无怪物
		"color": Color.BLUE
	},
	"herb_forest": {
		"name": "灵草森林",
		"description": "采集灵草，可能遇到灵兽",
		"cost": {"qi": 30},
		"rewards": {"pills": 2, "sect_contribution": 1, "spirit_stones": 3},
		"duration": 45.0,
		"unlock_realm": "beginner",
		"monsters": ["灵草蛇", "森林狼"],
		"color": Color.GREEN
	},
	"spirit_mine": {
		"name": "灵石矿洞",
		"description": "挖掘灵石，小心矿洞妖兽",
		"cost": {"qi": 50, "sect_contribution": 5},
		"rewards": {"spirit_stones": 15, "cultivation_exp": 3},
		"duration": 60.0,
		"unlock_realm": "practitioner",
		"monsters": ["石甲虫", "矿洞蝙蝠"],
		"color": Color.CYAN
	},
	"demon_valley": {
		"name": "妖兽谷",
		"description": "危险的妖兽聚集地，收获丰厚",
		"cost": {"qi": 80, "pills": 1},
		"rewards": {"spirit_stones": 25, "artifacts": 1, "cultivation_exp": 8},
		"duration": 90.0,
		"unlock_realm": "body_refining",
		"monsters": ["妖狼", "毒蝎", "火狐"],
		"color": Color.RED
	},
	"ancient_ruins": {
		"name": "古代遗迹",
		"description": "探索古迹寻找宝物和功法",
		"cost": {"qi": 100, "sect_contribution": 10},
		"rewards": {"techniques": 1, "artifacts": 2, "spirit_stones": 30},
		"duration": 120.0,
		"unlock_realm": "qi_refining",
		"monsters": ["守护傀儡", "幽灵武士"],
		"color": Color.PURPLE
	},
	"forbidden_land": {
		"name": "禁地深渊",
		"description": "极度危险的禁地，蕴含无上机缘",
		"cost": {"qi": 150, "pills": 3, "artifacts": 1},
		"rewards": {"cultivation_exp": 20, "techniques": 2, "spirit_stones": 50},
		"duration": 180.0,
		"unlock_realm": "foundation",
		"monsters": ["深渊魔兽", "上古凶灵", "禁地守护者"],
		"color": Color.DARK_RED
	}
}

# 游戏状态
var game_paused = false
var selected_cultivation_spot = ""

# 灵石获取系统
var daily_bonus_claimed = false
var daily_bonus_amount = 20
var last_bonus_day = -1
var random_event_timer = 0.0
var random_event_interval = 120.0  # 每2分钟检查一次随机事件

func _ready():
	# 初始化修仙游戏
	print("修仙游戏管理器已启动")
	print("当前境界：%s" % cultivation_realms[current_realm].name)
	start_production_timer()

func start_production_timer():
	# 创建生产计时器
	var timer = Timer.new()
	timer.wait_time = 1.0  # 每秒检查一次
	timer.timeout.connect(_on_production_tick)
	timer.autostart = true
	add_child(timer)

func _on_production_tick():
	# 处理所有修炼活动
	if game_paused:
		return

	# 更新修炼地点进度
	update_cultivation_progress()

	# 检查修炼经验并尝试突破境界
	check_realm_breakthrough()

	# 更新随机事件计时器
	update_random_events()

	# 检查每日奖励
	check_daily_bonus()

# 修仙境界管理
func check_realm_breakthrough():
	# 检查是否可以突破到下一个境界
	var current_exp = resources.get("cultivation_exp", 0)
	var next_realm = get_next_realm()

	if next_realm != "" and current_exp >= cultivation_realms[next_realm].exp_required:
		breakthrough_to_realm(next_realm)

func get_next_realm() -> String:
	# 获取下一个境界
	var current_level = cultivation_realms[current_realm].level
	for realm_key in cultivation_realms:
		var realm_data = cultivation_realms[realm_key]
		if realm_data.level == current_level + 1:
			return realm_key
	return ""

func breakthrough_to_realm(new_realm: String):
	# 突破到新境界
	var _old_realm_name = cultivation_realms[current_realm].name
	current_realm = new_realm
	var new_realm_name = cultivation_realms[current_realm].name

	print("恭喜！突破到 %s 境界！" % new_realm_name)
	realm_breakthrough.emit(new_realm)

	# 解锁新修炼地点
	unlock_cultivation_spots_for_realm(new_realm)

func unlock_cultivation_spots_for_realm(realm: String):
	# 解锁对应境界的修炼地点
	for spot_type in cultivation_spots:
		var spot_data = cultivation_spots[spot_type]
		if spot_data.unlock_realm == realm:
			print("解锁新修炼地点：%s" % spot_data.name)

func get_current_realm_info() -> Dictionary:
	# 获取当前境界信息
	return {
		"current": cultivation_realms[current_realm],
		"next": get_next_realm_info(),
		"progress": get_cultivation_progress()
	}

func get_next_realm_info() -> Dictionary:
	# 获取下一境界信息
	var next_realm = get_next_realm()
	if next_realm != "":
		return cultivation_realms[next_realm]
	return {}

func get_cultivation_progress() -> float:
	# 获取修炼进度百分比
	var current_exp = resources.get("cultivation_exp", 0)
	var next_realm = get_next_realm()

	if next_realm == "":
		return 1.0  # 已达到最高境界

	var current_required = cultivation_realms[current_realm].exp_required
	var next_required = cultivation_realms[next_realm].exp_required
	var progress_range = next_required - current_required
	var current_progress = current_exp - current_required

	return clamp(float(current_progress) / float(progress_range), 0.0, 1.0)

# 资源管理
func add_resource(resource_type: String, amount: int):
	if resource_type in resources:
		resources[resource_type] += amount
		resource_changed.emit(resource_type, resources[resource_type])
		print("获得 %d %s，当前总量：%d" % [amount, resource_type, resources[resource_type]])

		# 特殊处理修炼经验
		if resource_type == "cultivation_exp":
			cultivation_progress.emit(get_cultivation_progress())

func spend_resource(resource_type: String, amount: int) -> bool:
	if resource_type in resources and resources[resource_type] >= amount:
		resources[resource_type] -= amount
		resource_changed.emit(resource_type, resources[resource_type])
		print("消耗 %d %s，剩余：%d" % [amount, resource_type, resources[resource_type]])
		return true
	return false

func can_afford(cost: Dictionary) -> bool:
	for resource_type in cost:
		if resource_type not in resources or resources[resource_type] < cost[resource_type]:
			return false
	return true

func spend_resources(cost: Dictionary) -> bool:
	if not can_afford(cost):
		return false

	for resource_type in cost:
		spend_resource(resource_type, cost[resource_type])
	return true

# 修炼地点管理
func start_cultivation(spot_type: String) -> bool:
	if spot_type not in cultivation_spots:
		print("未知的修炼地点：", spot_type)
		return false

	var spot_data = cultivation_spots[spot_type]

	# 检查境界是否足够解锁此地点
	if not is_cultivation_spot_unlocked(spot_type):
		var required_realm = cultivation_realms[spot_data.unlock_realm].name
		print("境界不足！需要达到 %s 才能前往 %s" % [required_realm, spot_data.name])
		return false

	if not can_afford(spot_data.cost):
		print("资源不足，无法前往", spot_data.name)
		return false

	# 扣除资源
	if spend_resources(spot_data.cost):
		# 创建修炼活动实例
		var cultivation_activity = {
			"spot_type": spot_type,
			"spot_data": spot_data,
			"start_time": Time.get_ticks_msec() / 1000.0,
			"duration": spot_data.duration,
			"progress": 0.0
		}
		active_cultivation_spots.append(cultivation_activity)

		cultivation_started.emit(spot_type, spot_data.name)
		print("开始在 %s 修炼" % spot_data.name)
		return true

	return false

func is_cultivation_spot_unlocked(spot_type: String) -> bool:
	# 检查修炼地点是否已解锁
	if spot_type not in cultivation_spots:
		return false

	var spot_data = cultivation_spots[spot_type]
	var required_realm = spot_data.unlock_realm
	var current_level = cultivation_realms[current_realm].level
	var required_level = cultivation_realms[required_realm].level

	return current_level >= required_level

func get_unlocked_cultivation_spots() -> Array:
	# 获取已解锁的修炼地点列表
	var unlocked = []
	for spot_type in cultivation_spots:
		if is_cultivation_spot_unlocked(spot_type):
			unlocked.append(spot_type)
	return unlocked

func get_resource(resource_type: String) -> int:
	return resources.get(resource_type, 0)

func set_selected_cultivation_spot(spot_type: String):
	selected_cultivation_spot = spot_type
	print("选择修炼地点：", spot_type)

func pause_game():
	game_paused = true
	print("游戏暂停")

func resume_game():
	game_paused = false
	print("游戏继续")

# 修炼进度管理
func update_cultivation_progress():
	# 更新所有正在进行的修炼活动
	var current_time = Time.get_ticks_msec() / 1000.0
	var completed_activities = []

	for i in range(active_cultivation_spots.size()):
		var activity = active_cultivation_spots[i]
		var elapsed_time = current_time - activity.start_time
		activity.progress = elapsed_time / activity.duration

		# 检查是否完成
		if activity.progress >= 1.0:
			complete_cultivation(activity)
			completed_activities.append(i)

	# 移除已完成的活动（从后往前删除避免索引问题）
	for i in range(completed_activities.size() - 1, -1, -1):
		active_cultivation_spots.remove_at(completed_activities[i])

func complete_cultivation(activity: Dictionary):
	# 完成修炼活动，获得奖励
	var spot_data = activity.spot_data
	var spot_name = spot_data.name

	print("完成修炼：%s" % spot_name)

	# 获得基础奖励
	for resource_type in spot_data.rewards:
		var amount = spot_data.rewards[resource_type]
		add_resource(resource_type, amount)
		print("获得 %s: %d" % [resource_type, amount])

	# 发射完成信号
	cultivation_completed.emit(activity.spot_type, spot_data.rewards)

	# 处理怪物战斗
	if spot_data.monsters.size() > 0:
		handle_monster_encounter(spot_data.monsters, spot_name)

func handle_monster_encounter(monsters: Array, location: String):
	# 处理怪物遭遇
	if monsters.size() == 0:
		return

	var monster = monsters[randi() % monsters.size()]
	print("在 %s 遭遇了 %s！" % [location, monster])

	# 简单的战斗结果（基于境界等级）
	var current_level = cultivation_realms[current_realm].level
	var victory_chance = min(0.9, 0.3 + current_level * 0.1)  # 30%基础胜率，每个境界+10%

	if randf() < victory_chance:
		print("成功击败了 %s！" % monster)
		monster_encountered.emit(monster, location, true)
		# 战斗胜利奖励
		var bonus_exp = randi_range(2, 8)
		var bonus_stones = randi_range(1, 5)
		add_resource("cultivation_exp", bonus_exp)
		add_resource("spirit_stones", bonus_stones)
		print("战斗奖励：修炼经验 +%d，灵石 +%d" % [bonus_exp, bonus_stones])
	else:
		print("被 %s 击败了，损失了一些资源..." % monster)
		monster_encountered.emit(monster, location, false)
		# 战斗失败惩罚
		var lost_qi = randi_range(5, 15)
		spend_resource("qi", lost_qi)
		print("损失灵气：%d" % lost_qi)

# 灵石获取系统
func check_daily_bonus():
	# 检查每日奖励
	var current_day = Time.get_datetime_dict_from_system().day

	if last_bonus_day != current_day:
		last_bonus_day = current_day
		daily_bonus_claimed = false

		if not daily_bonus_claimed:
			daily_bonus_claimed = true
			add_resource("spirit_stones", daily_bonus_amount)
			print("🎁 每日奖励：获得 %d 灵石！" % daily_bonus_amount)

func update_random_events():
	# 更新随机事件
	random_event_timer += 1.0

	if random_event_timer >= random_event_interval:
		random_event_timer = 0.0
		trigger_random_event()

func trigger_random_event():
	# 触发随机事件
	var random_chance = randf()

	if random_chance < 0.3:  # 30%概率
		var events = [
			{
				"name": "发现灵石碎片",
				"description": "在修炼时意外发现了一些灵石碎片",
				"reward": {"spirit_stones": 5}
			},
			{
				"name": "师兄赠礼",
				"description": "师兄看你修炼刻苦，赠送了一些灵石",
				"reward": {"spirit_stones": 8}
			},
			{
				"name": "完成小任务",
				"description": "帮助同门完成了一个小任务",
				"reward": {"spirit_stones": 3, "sect_contribution": 2}
			},
			{
				"name": "拾得遗物",
				"description": "在门派后山发现了前辈遗留的宝物",
				"reward": {"spirit_stones": 12, "pills": 1}
			},
			{
				"name": "修炼顿悟",
				"description": "修炼时突然顿悟，获得了一些感悟",
				"reward": {"cultivation_exp": 10, "spirit_stones": 2}
			}
		]

		var event = events[randi() % events.size()]
		print("🌟 随机事件：%s - %s" % [event.name, event.description])

		for resource_type in event.reward:
			add_resource(resource_type, event.reward[resource_type])

func manual_spirit_stone_activities():
	# 手动灵石获取活动（可以添加到UI中）
	var activities = {
		"meditation_reward": {
			"name": "冥想奖励",
			"description": "深度冥想获得灵石奖励",
			"cost": {"qi": 50},
			"reward": {"spirit_stones": 6},
			"cooldown": 300  # 5分钟冷却
		},
		"herb_selling": {
			"name": "售卖灵草",
			"description": "将采集的灵草卖给门派",
			"cost": {"pills": 3},
			"reward": {"spirit_stones": 15},
			"cooldown": 600  # 10分钟冷却
		},
		"artifact_trading": {
			"name": "法器交易",
			"description": "与其他弟子交易法器",
			"cost": {"artifacts": 1},
			"reward": {"spirit_stones": 30},
			"cooldown": 900  # 15分钟冷却
		}
	}
	return activities

# 保存/加载系统（后续实现）
func save_game():
	# TODO: 实现保存功能
	pass

func load_game():
	# TODO: 实现加载功能
	pass
