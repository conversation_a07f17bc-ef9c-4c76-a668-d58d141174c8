extends Node

# 游戏管理器 - 负责管理游戏的核心逻辑
class_name GameManager

# 信号
signal resource_changed(resource_type: String, amount: int)
signal building_placed(building_type: String, position: Vector2)

# 游戏资源
var resources = {
	"coins": 100,      # 金币
	"energy": 50,      # 能量
	"population": 0    # 人口
}

# 建筑数据
var buildings = []
var building_types = {
	"house": {
		"name": "房屋",
		"cost": {"coins": 50},
		"production": {"population": 1},
		"production_rate": 5.0  # 每5秒产生一次
	},
	"generator": {
		"name": "发电厂",
		"cost": {"coins": 100},
		"production": {"energy": 2},
		"production_rate": 3.0  # 每3秒产生一次
	},
	"shop": {
		"name": "商店",
		"cost": {"coins": 75, "energy": 10},
		"production": {"coins": 5},
		"production_rate": 2.0  # 每2秒产生一次
	}
}

# 游戏状态
var game_paused = false
var selected_building_type = ""

func _ready():
	# 初始化游戏
	print("游戏管理器已启动")
	start_production_timer()

func start_production_timer():
	# 创建生产计时器
	var timer = Timer.new()
	timer.wait_time = 1.0  # 每秒检查一次
	timer.timeout.connect(_on_production_tick)
	timer.autostart = true
	add_child(timer)

func _on_production_tick():
	# 处理所有建筑的生产
	if game_paused:
		return
		
	for building in buildings:
		building.update_production()

# 资源管理
func add_resource(resource_type: String, amount: int):
	if resource_type in resources:
		resources[resource_type] += amount
		resource_changed.emit(resource_type, resources[resource_type])
		print("获得 %d %s，当前总量：%d" % [amount, resource_type, resources[resource_type]])

func spend_resource(resource_type: String, amount: int) -> bool:
	if resource_type in resources and resources[resource_type] >= amount:
		resources[resource_type] -= amount
		resource_changed.emit(resource_type, resources[resource_type])
		print("消耗 %d %s，剩余：%d" % [amount, resource_type, resources[resource_type]])
		return true
	return false

func can_afford(cost: Dictionary) -> bool:
	for resource_type in cost:
		if resource_type not in resources or resources[resource_type] < cost[resource_type]:
			return false
	return true

func spend_resources(cost: Dictionary) -> bool:
	if not can_afford(cost):
		return false
	
	for resource_type in cost:
		spend_resource(resource_type, cost[resource_type])
	return true

# 建筑管理
func place_building(building_type: String, position: Vector2) -> bool:
	if building_type not in building_types:
		print("未知的建筑类型：", building_type)
		return false
	
	var building_data = building_types[building_type]
	
	if not can_afford(building_data.cost):
		print("资源不足，无法建造", building_data.name)
		return false
	
	# 扣除资源
	if spend_resources(building_data.cost):
		# 创建建筑实例
		var building = Building.new()
		building.initialize(building_type, building_data, position)
		buildings.append(building)
		
		building_placed.emit(building_type, position)
		print("成功建造 %s 在位置 %s" % [building_data.name, position])
		return true
	
	return false

func get_resource(resource_type: String) -> int:
	return resources.get(resource_type, 0)

func set_selected_building_type(building_type: String):
	selected_building_type = building_type
	print("选择建筑类型：", building_type)

func pause_game():
	game_paused = true
	print("游戏暂停")

func resume_game():
	game_paused = false
	print("游戏继续")

# 保存/加载系统（后续实现）
func save_game():
	# TODO: 实现保存功能
	pass

func load_game():
	# TODO: 实现加载功能
	pass
