extends Node

# 修仙游戏管理器 - 负责管理修仙游戏的核心逻辑
class_name GameManager

# 信号
signal resource_changed(resource_type: String, amount: int)
signal building_placed(building_type: String, position: Vector2)
signal realm_breakthrough(new_realm: String)
signal cultivation_progress(progress: float)

# 修仙资源 - 个人修炼者视角
var resources = {
	"spirit_stones": 10,     # 灵石 - 基础货币（初始很少）
	"qi": 100,              # 灵气 - 修炼根本
	"cultivation_exp": 0,   # 修炼经验
	"sect_contribution": 0, # 门派贡献点
	"pills": 0,             # 丹药
	"artifacts": 0,         # 法器
	"techniques": 0         # 功法数量
}

# 修仙境界系统
var cultivation_realms = {
	# 凡人阶段
	"mortal": {"name": "凡人", "level": 0, "exp_required": 0},
	"beginner": {"name": "初学者", "level": 1, "exp_required": 100},
	"practitioner": {"name": "修行者", "level": 2, "exp_required": 300},

	# 修真境界
	"body_refining": {"name": "炼体期", "level": 3, "exp_required": 600},
	"qi_refining": {"name": "炼气期", "level": 4, "exp_required": 1000},
	"foundation": {"name": "筑基期", "level": 5, "exp_required": 1500},
	"core_formation": {"name": "结丹期", "level": 6, "exp_required": 2500},
	"nascent_soul": {"name": "元婴期", "level": 7, "exp_required": 4000},
	"soul_transformation": {"name": "化神期", "level": 8, "exp_required": 6000},
	"void_integration": {"name": "合道期", "level": 9, "exp_required": 9000},
	"mahayana": {"name": "大乘期", "level": 10, "exp_required": 13000},
	"tribulation": {"name": "渡劫期", "level": 11, "exp_required": 18000}
}

var current_realm = "mortal"
var current_cultivation_exp = 0

# 修炼活动 - 个人修炼者视角
var buildings = []
var building_types = {
	"meditation_spot": {
		"name": "修炼之地",
		"description": "安静的修炼场所，提升修为",
		"cost": {"qi": 20},
		"production": {"cultivation_exp": 3, "qi": 1},
		"production_rate": 4.0,
		"unlock_realm": "mortal",
		"color": Color.BLUE
	},
	"herb_gathering": {
		"name": "采药点",
		"description": "采集灵草炼制丹药",
		"cost": {"qi": 30, "sect_contribution": 5},
		"production": {"pills": 1, "sect_contribution": 2},
		"production_rate": 8.0,
		"unlock_realm": "beginner",
		"color": Color.GREEN
	},
	"sect_mission": {
		"name": "门派任务",
		"description": "完成门派任务获得奖励",
		"cost": {"qi": 40},
		"production": {"spirit_stones": 8, "sect_contribution": 3},
		"production_rate": 10.0,
		"unlock_realm": "practitioner",
		"color": Color.ORANGE
	},
	"spirit_mining": {
		"name": "灵石矿挖掘",
		"description": "在灵石矿脉中挖掘原石",
		"cost": {"qi": 60, "sect_contribution": 8},
		"production": {"spirit_stones": 15, "cultivation_exp": 2},
		"production_rate": 15.0,
		"unlock_realm": "body_refining",
		"color": Color.CYAN
	},
	"treasure_hunting": {
		"name": "寻宝探险",
		"description": "探索古迹寻找宝物",
		"cost": {"qi": 80, "pills": 2},
		"production": {"spirit_stones": 25, "artifacts": 1},
		"production_rate": 20.0,
		"unlock_realm": "qi_refining",
		"color": Color.GOLD
	},
	"technique_study": {
		"name": "功法研习",
		"description": "学习新的修炼功法",
		"cost": {"spirit_stones": 50, "sect_contribution": 10},
		"production": {"techniques": 1, "cultivation_exp": 5},
		"production_rate": 15.0,
		"unlock_realm": "body_refining",
		"color": Color.PURPLE
	},
	"artifact_refining": {
		"name": "炼器修行",
		"description": "炼制和改良法器",
		"cost": {"spirit_stones": 100, "pills": 3},
		"production": {"artifacts": 1, "cultivation_exp": 4},
		"production_rate": 12.0,
		"unlock_realm": "qi_refining",
		"color": Color.MAGENTA
	},
	"inner_alchemy": {
		"name": "内丹修炼",
		"description": "高深的内丹修炼法",
		"cost": {"spirit_stones": 200, "techniques": 2},
		"production": {"cultivation_exp": 10, "qi": 5},
		"production_rate": 8.0,
		"unlock_realm": "foundation",
		"color": Color.GOLD
	}
}

# 游戏状态
var game_paused = false
var selected_building_type = ""

# 灵石获取系统
var daily_bonus_claimed = false
var daily_bonus_amount = 20
var last_bonus_day = -1
var random_event_timer = 0.0
var random_event_interval = 120.0  # 每2分钟检查一次随机事件

func _ready():
	# 初始化修仙游戏
	print("修仙游戏管理器已启动")
	print("当前境界：%s" % cultivation_realms[current_realm].name)
	start_production_timer()

func start_production_timer():
	# 创建生产计时器
	var timer = Timer.new()
	timer.wait_time = 1.0  # 每秒检查一次
	timer.timeout.connect(_on_production_tick)
	timer.autostart = true
	add_child(timer)

func _on_production_tick():
	# 处理所有建筑的生产
	if game_paused:
		return

	for building in buildings:
		building.update_production()

	# 检查修炼经验并尝试突破境界
	check_realm_breakthrough()

	# 更新随机事件计时器
	update_random_events()

	# 检查每日奖励
	check_daily_bonus()

# 修仙境界管理
func check_realm_breakthrough():
	# 检查是否可以突破到下一个境界
	var current_exp = resources.get("cultivation_exp", 0)
	var next_realm = get_next_realm()

	if next_realm != "" and current_exp >= cultivation_realms[next_realm].exp_required:
		breakthrough_to_realm(next_realm)

func get_next_realm() -> String:
	# 获取下一个境界
	var current_level = cultivation_realms[current_realm].level
	for realm_key in cultivation_realms:
		var realm_data = cultivation_realms[realm_key]
		if realm_data.level == current_level + 1:
			return realm_key
	return ""

func breakthrough_to_realm(new_realm: String):
	# 突破到新境界
	var _old_realm_name = cultivation_realms[current_realm].name
	current_realm = new_realm
	var new_realm_name = cultivation_realms[current_realm].name

	print("恭喜！突破到 %s 境界！" % new_realm_name)
	realm_breakthrough.emit(new_realm)

	# 解锁新建筑
	unlock_buildings_for_realm(new_realm)

func unlock_buildings_for_realm(realm: String):
	# 解锁对应境界的建筑
	for building_type in building_types:
		var building_data = building_types[building_type]
		if building_data.unlock_realm == realm:
			print("解锁新建筑：%s" % building_data.name)

func get_current_realm_info() -> Dictionary:
	# 获取当前境界信息
	return {
		"current": cultivation_realms[current_realm],
		"next": get_next_realm_info(),
		"progress": get_cultivation_progress()
	}

func get_next_realm_info() -> Dictionary:
	# 获取下一境界信息
	var next_realm = get_next_realm()
	if next_realm != "":
		return cultivation_realms[next_realm]
	return {}

func get_cultivation_progress() -> float:
	# 获取修炼进度百分比
	var current_exp = resources.get("cultivation_exp", 0)
	var next_realm = get_next_realm()

	if next_realm == "":
		return 1.0  # 已达到最高境界

	var current_required = cultivation_realms[current_realm].exp_required
	var next_required = cultivation_realms[next_realm].exp_required
	var progress_range = next_required - current_required
	var current_progress = current_exp - current_required

	return clamp(float(current_progress) / float(progress_range), 0.0, 1.0)

# 资源管理
func add_resource(resource_type: String, amount: int):
	if resource_type in resources:
		resources[resource_type] += amount
		resource_changed.emit(resource_type, resources[resource_type])
		print("获得 %d %s，当前总量：%d" % [amount, resource_type, resources[resource_type]])

		# 特殊处理修炼经验
		if resource_type == "cultivation_exp":
			cultivation_progress.emit(get_cultivation_progress())

func spend_resource(resource_type: String, amount: int) -> bool:
	if resource_type in resources and resources[resource_type] >= amount:
		resources[resource_type] -= amount
		resource_changed.emit(resource_type, resources[resource_type])
		print("消耗 %d %s，剩余：%d" % [amount, resource_type, resources[resource_type]])
		return true
	return false

func can_afford(cost: Dictionary) -> bool:
	for resource_type in cost:
		if resource_type not in resources or resources[resource_type] < cost[resource_type]:
			return false
	return true

func spend_resources(cost: Dictionary) -> bool:
	if not can_afford(cost):
		return false

	for resource_type in cost:
		spend_resource(resource_type, cost[resource_type])
	return true

# 建筑管理
func place_building(building_type: String, position: Vector2) -> bool:
	if building_type not in building_types:
		print("未知的建筑类型：", building_type)
		return false

	var building_data = building_types[building_type]

	# 检查境界是否足够解锁此建筑
	if not is_building_unlocked(building_type):
		var required_realm = cultivation_realms[building_data.unlock_realm].name
		print("境界不足！需要达到 %s 才能建造 %s" % [required_realm, building_data.name])
		return false

	if not can_afford(building_data.cost):
		print("资源不足，无法建造", building_data.name)
		return false

	# 扣除资源
	if spend_resources(building_data.cost):
		# 创建建筑实例
		var building = Building.new()
		building.initialize(building_type, building_data, position)
		buildings.append(building)

		building_placed.emit(building_type, position)
		print("成功建造 %s 在位置 %s" % [building_data.name, position])
		return true

	return false

func is_building_unlocked(building_type: String) -> bool:
	# 检查建筑是否已解锁
	if building_type not in building_types:
		return false

	var building_data = building_types[building_type]
	var required_realm = building_data.unlock_realm
	var current_level = cultivation_realms[current_realm].level
	var required_level = cultivation_realms[required_realm].level

	return current_level >= required_level

func get_unlocked_buildings() -> Array:
	# 获取已解锁的建筑列表
	var unlocked = []
	for building_type in building_types:
		if is_building_unlocked(building_type):
			unlocked.append(building_type)
	return unlocked

func get_resource(resource_type: String) -> int:
	return resources.get(resource_type, 0)

func set_selected_building_type(building_type: String):
	selected_building_type = building_type
	print("选择建筑类型：", building_type)

func pause_game():
	game_paused = true
	print("游戏暂停")

func resume_game():
	game_paused = false
	print("游戏继续")

# 灵石获取系统
func check_daily_bonus():
	# 检查每日奖励
	var current_day = Time.get_datetime_dict_from_system().day

	if last_bonus_day != current_day:
		last_bonus_day = current_day
		daily_bonus_claimed = false

		if not daily_bonus_claimed:
			daily_bonus_claimed = true
			add_resource("spirit_stones", daily_bonus_amount)
			print("🎁 每日奖励：获得 %d 灵石！" % daily_bonus_amount)

func update_random_events():
	# 更新随机事件
	random_event_timer += 1.0

	if random_event_timer >= random_event_interval:
		random_event_timer = 0.0
		trigger_random_event()

func trigger_random_event():
	# 触发随机事件
	var random_chance = randf()

	if random_chance < 0.3:  # 30%概率
		var events = [
			{
				"name": "发现灵石碎片",
				"description": "在修炼时意外发现了一些灵石碎片",
				"reward": {"spirit_stones": 5}
			},
			{
				"name": "师兄赠礼",
				"description": "师兄看你修炼刻苦，赠送了一些灵石",
				"reward": {"spirit_stones": 8}
			},
			{
				"name": "完成小任务",
				"description": "帮助同门完成了一个小任务",
				"reward": {"spirit_stones": 3, "sect_contribution": 2}
			},
			{
				"name": "拾得遗物",
				"description": "在门派后山发现了前辈遗留的宝物",
				"reward": {"spirit_stones": 12, "pills": 1}
			},
			{
				"name": "修炼顿悟",
				"description": "修炼时突然顿悟，获得了一些感悟",
				"reward": {"cultivation_exp": 10, "spirit_stones": 2}
			}
		]

		var event = events[randi() % events.size()]
		print("🌟 随机事件：%s - %s" % [event.name, event.description])

		for resource_type in event.reward:
			add_resource(resource_type, event.reward[resource_type])

func manual_spirit_stone_activities():
	# 手动灵石获取活动（可以添加到UI中）
	var activities = {
		"meditation_reward": {
			"name": "冥想奖励",
			"description": "深度冥想获得灵石奖励",
			"cost": {"qi": 50},
			"reward": {"spirit_stones": 6},
			"cooldown": 300  # 5分钟冷却
		},
		"herb_selling": {
			"name": "售卖灵草",
			"description": "将采集的灵草卖给门派",
			"cost": {"pills": 3},
			"reward": {"spirit_stones": 15},
			"cooldown": 600  # 10分钟冷却
		},
		"artifact_trading": {
			"name": "法器交易",
			"description": "与其他弟子交易法器",
			"cost": {"artifacts": 1},
			"reward": {"spirit_stones": 30},
			"cooldown": 900  # 15分钟冷却
		}
	}
	return activities

# 保存/加载系统（后续实现）
func save_game():
	# TODO: 实现保存功能
	pass

func load_game():
	# TODO: 实现加载功能
	pass
