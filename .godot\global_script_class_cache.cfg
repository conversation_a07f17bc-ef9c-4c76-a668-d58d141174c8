list=[{
"base": &"Node2D",
"class": &"Building",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/buildings/Building.gd"
}, {
"base": &"Control",
"class": &"CultivationUIManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/CultivationUIManager.gd"
}, {
"base": &"Node",
"class": &"GameManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/managers/GameManager.gd"
}, {
"base": &"Node2D",
"class": &"Main",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/main/Main.gd"
}, {
"base": &"Node2D",
"class": &"MainFixed",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/main/MainFixed.gd"
}, {
"base": &"Control",
"class": &"UIManager",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://scripts/ui/UIManager.gd"
}, {
"base": &"Control",
"class": &"UIManagerFixed",
"icon": "",
"is_abstract": false,
"is_tool": false,
"language": &"GDScript",
"path": "res://临时修复.gd"
}]
