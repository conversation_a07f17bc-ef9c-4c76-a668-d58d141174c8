# 修仙放置游戏 - 纯文字版

一个使用Godot引擎开发的修仙主题纯文字放置类游戏，体验从凡人到创世的修炼之路。

## 🎮 游戏概念

这是一个修仙主题的纯文字放置类游戏，玩家可以：
- 🏗️ 建造各种修仙建筑（静修室🧘、灵药园🌿、炼丹房⚗️等）
- 💎 建筑自动产生修仙资源（灵石、灵气、丹药等）
- ⭐ 通过修炼经验提升境界，解锁更高级的建筑
- 🌟 体验完整的修仙境界体系，从凡人到创世境界
- 📜 实时游戏日志记录修仙历程

## 🏗️ 项目结构

```
├── scenes/                 # 场景文件
│   ├── main/              # 主游戏场景
│   ├── ui/                # 用户界面
│   └── buildings/         # 修仙建筑场景
├── scripts/               # 脚本文件
│   ├── managers/          # 游戏管理器（修仙系统核心）
│   ├── buildings/         # 建筑脚本
│   └── ui/                # UI脚本
├── assets/                # 游戏资源
│   ├── textures/          # 图片资源
│   ├── audio/             # 音频资源
│   └── fonts/             # 字体资源
└── project.godot          # Godot项目配置
```

## ⚡ 修仙境界体系

### 凡人阶段
- **凡人** - 初始状态
- **初学者** - 开始修炼
- **修行者** - 具备基础功夫

### 修真境界
- **炼体期** - 锻炼肉身
- **炼气期** - 修炼灵气
- **筑基期** - 筑建根基
- **结丹期** - 凝结金丹
- **元婴期** - 孕育元婴
- **化神期** - 神魂化形
- **合道期** - 与道合一
- **大乘期** - 大乘境界
- **渡劫期** - 渡过天劫

### 修仙境界
- **人仙期** - **真仙期** - **地仙期** - **天仙期**
- **金仙期** - **玄仙期** - **神仙期**

### 修神境界
- **下位神** - **中位神** - **上位神**
- **至尊神** - **王者神** - **圣者神**

### 混沌境界
- **圣人境** - **圣尊境** - **圣王境** - **圣皇境**

### 创世境界
- **玄黄界尊** - **鸿蒙界尊** - **混沌界尊**

## 🏛️ 修仙建筑系统

### 基础建筑
- **🧘 静修室** - 弟子修炼的基础场所，产生灵气和修炼经验
- **🌿 灵药园** - 种植灵草的园地，产生丹药和灵气

### 进阶建筑
- **⚗️ 炼丹房** - 炼制丹药的场所，产生灵石和修炼经验
- **🔨 炼器坊** - 锻造法器的工坊，产生法器和灵石
- **🏛️ 弟子堂** - 招收弟子的场所，产生弟子和功德
- **🏦 宝库** - 存放珍宝的地方，产生灵石和功德

## 💎 资源系统

- **💎 灵石** - 基础货币，用于建造大部分建筑
- **🌀 灵气** - 修炼必需资源，高级建筑需要消耗
- **👥 弟子** - 门派人员，影响建筑效率
- **⭐ 修炼经验** - 提升境界的关键资源
- **💊 丹药** - 辅助修炼的珍贵资源
- **⚔️ 法器** - 高级装备，用于建造顶级建筑
- **🕉️ 功德** - 特殊资源，某些建筑需要消耗

## 📜 游戏特色

### 纯文字体验
- 🎨 使用Emoji图标代替图片，营造丰富的视觉体验
- 📊 清晰的文字界面，专注于数据和策略
- 📝 实时游戏日志，记录每一个重要时刻
- 🎯 简洁明了的操作界面，易于上手

### 沉浸式修仙
- 🌟 完整的境界突破体验
- 📈 数据驱动的成长系统
- 🏗️ 策略性的建筑布局
- ⏰ 放置类游戏的轻松节奏

## 🚀 开发计划

### 阶段1：核心系统 ✅
- [x] 修仙境界系统
- [x] 资源管理系统
- [x] 建筑放置系统
- [x] 境界突破机制

### 阶段2：游戏内容 ✅
- [x] 修仙主题建筑
- [x] 修仙UI界面
- [x] 建筑解锁系统

### 阶段3：优化完善
- [ ] 音效和视觉效果
- [ ] 更多高级建筑
- [ ] 存档系统
- [ ] 成就系统

## 技术要求

- Godot 4.2+
- 目标平台：PC (Windows)
- 分辨率：1280x720

## 开始开发

1. 在Godot中打开项目
2. 从Main场景开始开发
3. 按照开发计划逐步实现功能
