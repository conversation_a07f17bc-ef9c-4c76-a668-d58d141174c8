# 2D放置游戏

一个使用Godot引擎开发的简单2D放置类游戏。

## 游戏概念

这是一个放置类游戏，玩家可以：
- 点击建造各种建筑物
- 建筑物自动产生资源
- 使用资源升级建筑或购买新建筑
- 通过策略性放置建筑来最大化收益

## 项目结构

```
├── scenes/                 # 场景文件
│   ├── main/              # 主游戏场景
│   ├── ui/                # 用户界面
│   └── buildings/         # 建筑物场景
├── scripts/               # 脚本文件
│   ├── managers/          # 游戏管理器
│   ├── buildings/         # 建筑物脚本
│   └── ui/                # UI脚本
├── assets/                # 游戏资源
│   ├── textures/          # 图片资源
│   ├── audio/             # 音频资源
│   └── fonts/             # 字体资源
└── project.godot          # Godot项目配置
```

## 开发计划

### 阶段1：核心系统
- [ ] 基础游戏场景
- [ ] 资源管理系统
- [ ] 建筑放置系统

### 阶段2：游戏内容
- [ ] 基础建筑类型
- [ ] UI界面
- [ ] 升级系统

### 阶段3：优化完善
- [ ] 音效和视觉效果
- [ ] 平衡性调整
- [ ] 存档系统

## 技术要求

- Godot 4.2+
- 目标平台：PC (Windows)
- 分辨率：1280x720

## 开始开发

1. 在Godot中打开项目
2. 从Main场景开始开发
3. 按照开发计划逐步实现功能
