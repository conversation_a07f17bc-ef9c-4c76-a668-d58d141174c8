# 2D放置游戏开发指南

## 🎯 项目概述

这是一个使用Godot 4.2开发的2D放置类游戏项目。玩家可以在游戏世界中放置不同类型的建筑，建筑会自动产生资源，玩家使用资源来建造更多建筑或升级现有建筑。

## 📁 项目结构说明

### 核心脚本
- `scripts/managers/GameManager.gd` - 游戏核心管理器，处理资源、建筑管理
- `scripts/main/Main.gd` - 主场景脚本，负责场景初始化和协调
- `scripts/ui/UIManager.gd` - UI管理器，处理用户界面
- `scripts/buildings/Building.gd` - 建筑基类，所有建筑的基础功能

### 场景组织
- `scenes/main/` - 主游戏场景
- `scenes/ui/` - UI相关场景
- `scenes/buildings/` - 建筑场景预制体

## 🚀 开发步骤

### 第一步：在Godot中设置项目
1. 打开Godot引擎
2. 点击"导入"，选择项目文件夹
3. 打开`project.godot`文件

### 第二步：创建主场景
1. 在Godot中创建新场景
2. 添加Node2D作为根节点，命名为"Main"
3. 将`scripts/main/Main.gd`脚本附加到根节点
4. 保存场景为`scenes/main/Main.tscn`
5. 在项目设置中将此场景设为主场景

### 第三步：测试基础功能
运行项目，应该能看到：
- 绿色背景的游戏世界
- 右侧的建筑选择面板
- 左上角的资源显示面板
- 可以点击建筑按钮选择建筑类型
- 在游戏区域点击可以放置建筑

## 🎮 游戏机制

### 资源系统
- **金币**: 主要货币，用于购买大部分建筑
- **能量**: 高级建筑需要的资源
- **人口**: 由房屋产生，可能用于解锁高级建筑

### 建筑类型
1. **房屋** (蓝色)
   - 成本: 50金币
   - 产出: 1人口/5秒

2. **发电厂** (黄色)
   - 成本: 100金币
   - 产出: 2能量/3秒

3. **商店** (绿色)
   - 成本: 75金币 + 10能量
   - 产出: 5金币/2秒

### 操作方式
- **WASD/方向键**: 移动摄像机
- **鼠标滚轮**: 缩放视图
- **左键点击**: 选择建筑或放置建筑
- **右侧面板**: 选择要建造的建筑类型

## 🔧 扩展建议

### 短期改进
1. **视觉优化**
   - 添加真实的建筑图片/精灵
   - 改善UI设计
   - 添加动画效果

2. **游戏性增强**
   - 添加更多建筑类型
   - 实现建筑升级系统
   - 添加成就系统

3. **用户体验**
   - 添加音效和背景音乐
   - 实现保存/加载功能
   - 添加设置菜单

### 长期扩展
1. **高级功能**
   - 多层级解锁系统
   - 特殊建筑和组合效果
   - 随机事件系统

2. **技术优化**
   - 性能优化
   - 更好的存档系统
   - 多平台适配

## 🐛 调试技巧

1. **查看控制台输出**: 游戏运行时查看Godot的输出面板
2. **使用调试模式**: 在调试模式下运行可以看到网格线
3. **检查节点树**: 确保所有脚本正确附加到节点
4. **资源监控**: 观察资源数值变化是否正常

## 📝 常见问题

**Q: 点击建筑按钮没有反应？**
A: 检查GameManager是否正确初始化，确保脚本路径正确。

**Q: 建筑放置后看不到？**
A: 检查Building.gd脚本是否正确创建了视觉元素。

**Q: 资源不更新？**
A: 确保信号连接正确，检查生产计时器是否正常工作。

## 🎉 下一步

项目基础框架已经搭建完成！现在您可以：
1. 在Godot中打开项目
2. 创建主场景并测试基础功能
3. 根据需要调整和扩展功能
4. 添加更多建筑类型和游戏内容

祝您开发愉快！🚀
