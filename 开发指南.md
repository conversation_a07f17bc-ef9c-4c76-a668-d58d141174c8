# 修仙放置游戏开发指南

## 🎯 项目概述

这是一个使用Godot 4.2开发的修仙主题2D放置类游戏项目。玩家扮演修仙者，通过建造各种修仙建筑来获取资源，提升修为境界，体验从凡人到创世境界的完整修仙之路。

## 📁 项目结构说明

### 核心脚本
- `scripts/managers/GameManager.gd` - 修仙游戏核心管理器，处理修仙资源、境界系统、建筑管理
- `scripts/main/Main.gd` - 主场景脚本，负责场景初始化和协调
- `scripts/ui/UIManager.gd` - 修仙UI管理器，处理修仙界面和境界显示
- `scripts/buildings/Building.gd` - 修仙建筑基类，所有修仙建筑的基础功能

### 场景组织
- `scenes/main/` - 主游戏场景
- `scenes/ui/` - 修仙UI相关场景
- `scenes/buildings/` - 修仙建筑场景预制体

## 🚀 开发步骤

### 第一步：在Godot中设置项目
1. 打开Godot引擎
2. 点击"导入"，选择项目文件夹
3. 打开`project.godot`文件

### 第二步：创建主场景
1. 在Godot中创建新场景
2. 添加Node2D作为根节点，命名为"Main"
3. 将`scripts/main/Main.gd`脚本附加到根节点
4. 保存场景为`scenes/main/Main.tscn`
5. 在项目设置中将此场景设为主场景

### 第三步：测试基础功能
运行项目，应该能看到：
- 绿色背景的修仙世界
- 右侧的修仙建筑选择面板
- 左上角的修仙资源显示面板
- 境界显示面板和修炼进度条
- 可以点击建筑按钮选择建筑类型
- 在游戏区域点击可以放置修仙建筑

## 🎮 修仙游戏机制

### 修仙资源系统
- **灵石**: 基础货币，用于建造大部分建筑
- **灵气**: 修炼必需资源，高级建筑需要消耗
- **弟子**: 门派人员，影响建筑效率
- **修炼经验**: 提升境界的关键资源
- **丹药**: 辅助修炼的珍贵资源
- **法器**: 高级装备，用于建造顶级建筑
- **功德**: 特殊资源，某些建筑需要消耗

### 修仙建筑类型
1. **静修室** (蓝色)
   - 成本: 50灵石
   - 产出: 1灵气/5秒 + 2修炼经验/5秒
   - 解锁: 凡人境界

2. **灵药园** (绿色)
   - 成本: 100灵石 + 20灵气
   - 产出: 1丹药/8秒 + 2灵气/8秒
   - 解锁: 初学者境界

3. **炼丹房** (红色)
   - 成本: 200灵石 + 5丹药
   - 产出: 10灵石/6秒 + 5修炼经验/6秒
   - 解锁: 修行者境界

4. **炼器坊** (橙色)
   - 成本: 300灵石 + 50灵气
   - 产出: 1法器/10秒 + 5灵石/10秒
   - 解锁: 炼体期

5. **弟子堂** (紫色)
   - 成本: 150灵石 + 10功德
   - 产出: 1弟子/15秒 + 2功德/15秒
   - 解锁: 炼气期

6. **宝库** (金色)
   - 成本: 500灵石 + 3法器
   - 产出: 20灵石/12秒 + 5功德/12秒
   - 解锁: 筑基期

### 境界突破系统
- 通过获得修炼经验自动提升境界
- 每个境界需要达到特定的修炼经验值
- 突破新境界会解锁新的建筑类型
- 境界进度条显示当前修炼进度

### 操作方式
- **WASD/方向键**: 移动摄像机
- **鼠标滚轮**: 缩放视图
- **左键点击**: 选择建筑或放置建筑
- **右侧面板**: 选择要建造的修仙建筑类型

## 🔧 扩展建议

### 短期改进
1. **视觉优化**
   - 添加真实的建筑图片/精灵
   - 改善UI设计
   - 添加动画效果

2. **游戏性增强**
   - 添加更多建筑类型
   - 实现建筑升级系统
   - 添加成就系统

3. **用户体验**
   - 添加音效和背景音乐
   - 实现保存/加载功能
   - 添加设置菜单

### 长期扩展
1. **高级功能**
   - 多层级解锁系统
   - 特殊建筑和组合效果
   - 随机事件系统

2. **技术优化**
   - 性能优化
   - 更好的存档系统
   - 多平台适配

## 🐛 调试技巧

1. **查看控制台输出**: 游戏运行时查看Godot的输出面板
2. **使用调试模式**: 在调试模式下运行可以看到网格线
3. **检查节点树**: 确保所有脚本正确附加到节点
4. **资源监控**: 观察资源数值变化是否正常

## 📝 常见问题

**Q: 点击建筑按钮没有反应？**
A: 检查GameManager是否正确初始化，确保脚本路径正确。

**Q: 建筑放置后看不到？**
A: 检查Building.gd脚本是否正确创建了视觉元素。

**Q: 资源不更新？**
A: 确保信号连接正确，检查生产计时器是否正常工作。

## 🎉 下一步

项目基础框架已经搭建完成！现在您可以：
1. 在Godot中打开项目
2. 创建主场景并测试基础功能
3. 根据需要调整和扩展功能
4. 添加更多建筑类型和游戏内容

祝您开发愉快！🚀
