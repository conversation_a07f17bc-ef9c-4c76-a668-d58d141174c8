extends Control

# 修仙UI管理器 - 修炼地点选择版本
class_name CultivationUIManager

# UI元素引用
var resource_panel: Panel
var cultivation_panel: Panel
var realm_panel: Panel
var spirit_stones_label: Label
var qi_label: Label
var sect_contribution_label: Label
var cultivation_exp_label: Label
var pills_label: Label
var artifacts_label: Label
var techniques_label: Label
var realm_label: Label
var progress_bar: ProgressBar

# 修炼地点按钮
var cultivation_buttons = {}

# 游戏管理器引用
var game_manager: GameManager

func _ready():
	# 延迟获取游戏管理器引用，等待Main场景完全初始化
	call_deferred("setup_game_manager_connection")

	# 创建UI元素
	setup_ui()

func setup_game_manager_connection():
	# 获取游戏管理器引用
	game_manager = get_node("../../GameManager")

	# 连接信号
	if game_manager:
		game_manager.resource_changed.connect(_on_resource_changed)
		game_manager.realm_breakthrough.connect(_on_realm_breakthrough)
		game_manager.cultivation_progress.connect(_on_cultivation_progress)
		game_manager.cultivation_started.connect(_on_cultivation_started)
		game_manager.cultivation_completed.connect(_on_cultivation_completed)
		game_manager.monster_encountered.connect(_on_monster_encountered)

		# 初始化资源显示
		update_resource_display()

		# 创建修炼地点按钮
		create_cultivation_buttons()
	else:
		print("警告：无法找到GameManager")

func setup_ui():
	# 创建资源显示面板
	create_resource_panel()

	# 创建境界显示面板
	create_realm_panel()

	# 创建修炼地点选择面板
	create_cultivation_panel()

func create_resource_panel():
	# 修仙资源面板 - 纯文字版本
	resource_panel = Panel.new()
	resource_panel.size = Vector2(500, 250)
	resource_panel.position = Vector2(10, 10)
	resource_panel.add_theme_color_override("bg_color", Color(0.1, 0.1, 0.2, 0.9))
	add_child(resource_panel)

	# 标题
	var title = Label.new()
	title.text = "═══ 修仙资源 ═══"
	title.position = Vector2(10, 5)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	resource_panel.add_child(title)

	# 创建资源标签
	create_resource_labels()

func create_resource_labels():
	# 灵石标签
	spirit_stones_label = Label.new()
	spirit_stones_label.text = "💎 灵石: 10"
	spirit_stones_label.position = Vector2(10, 35)
	spirit_stones_label.add_theme_font_size_override("font_size", 14)
	spirit_stones_label.add_theme_color_override("font_color", Color.CYAN)
	resource_panel.add_child(spirit_stones_label)

	# 灵气标签
	qi_label = Label.new()
	qi_label.text = "🌀 灵气: 100"
	qi_label.position = Vector2(10, 60)
	qi_label.add_theme_font_size_override("font_size", 14)
	qi_label.add_theme_color_override("font_color", Color.LIGHT_BLUE)
	resource_panel.add_child(qi_label)

	# 门派贡献标签
	sect_contribution_label = Label.new()
	sect_contribution_label.text = "🏛️ 门派贡献: 0"
	sect_contribution_label.position = Vector2(10, 85)
	sect_contribution_label.add_theme_font_size_override("font_size", 14)
	sect_contribution_label.add_theme_color_override("font_color", Color.LIGHT_GREEN)
	resource_panel.add_child(sect_contribution_label)

	# 修炼经验标签
	cultivation_exp_label = Label.new()
	cultivation_exp_label.text = "⭐ 修炼经验: 0"
	cultivation_exp_label.position = Vector2(10, 110)
	cultivation_exp_label.add_theme_font_size_override("font_size", 14)
	cultivation_exp_label.add_theme_color_override("font_color", Color.YELLOW)
	resource_panel.add_child(cultivation_exp_label)

	# 丹药标签
	pills_label = Label.new()
	pills_label.text = "💊 丹药: 0"
	pills_label.position = Vector2(250, 35)
	pills_label.add_theme_font_size_override("font_size", 14)
	pills_label.add_theme_color_override("font_color", Color.ORANGE)
	resource_panel.add_child(pills_label)

	# 法器标签
	artifacts_label = Label.new()
	artifacts_label.text = "⚔️ 法器: 0"
	artifacts_label.position = Vector2(250, 60)
	artifacts_label.add_theme_font_size_override("font_size", 14)
	artifacts_label.add_theme_color_override("font_color", Color.MAGENTA)
	resource_panel.add_child(artifacts_label)

	# 功法标签
	techniques_label = Label.new()
	techniques_label.text = "📜 功法: 0"
	techniques_label.position = Vector2(250, 85)
	techniques_label.add_theme_font_size_override("font_size", 14)
	techniques_label.add_theme_color_override("font_color", Color.LIGHT_CORAL)
	resource_panel.add_child(techniques_label)

func create_realm_panel():
	# 境界显示面板 - 纯文字版本
	realm_panel = Panel.new()
	realm_panel.size = Vector2(500, 120)
	realm_panel.position = Vector2(10, 270)
	realm_panel.add_theme_color_override("bg_color", Color(0.2, 0.1, 0.1, 0.9))
	add_child(realm_panel)

	# 境界标题
	var title = Label.new()
	title.text = "═══ 修炼境界 ═══"
	title.position = Vector2(10, 5)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	realm_panel.add_child(title)

	# 当前境界标签
	realm_label = Label.new()
	realm_label.text = "🏔️ 当前境界: 凡人"
	realm_label.position = Vector2(10, 35)
	realm_label.add_theme_font_size_override("font_size", 16)
	realm_label.add_theme_color_override("font_color", Color.LIGHT_CYAN)
	realm_panel.add_child(realm_label)

	# 修炼进度条（文字版）
	progress_bar = ProgressBar.new()
	progress_bar.size = Vector2(400, 25)
	progress_bar.position = Vector2(10, 65)
	progress_bar.min_value = 0.0
	progress_bar.max_value = 1.0
	progress_bar.value = 0.0
	progress_bar.add_theme_color_override("fill", Color.GOLD)
	progress_bar.add_theme_color_override("background", Color(0.3, 0.3, 0.3))
	realm_panel.add_child(progress_bar)

	# 进度标签
	var progress_label = Label.new()
	progress_label.text = "⚡ 突破进度"
	progress_label.position = Vector2(420, 65)
	progress_label.add_theme_font_size_override("font_size", 12)
	progress_label.add_theme_color_override("font_color", Color.YELLOW)
	realm_panel.add_child(progress_label)

func create_cultivation_panel():
	# 修炼地点面板 - 纯文字版本
	cultivation_panel = Panel.new()
	cultivation_panel.size = Vector2(350, 500)
	cultivation_panel.position = Vector2(get_viewport().size.x - 360, 10)
	cultivation_panel.add_theme_color_override("bg_color", Color(0.1, 0.2, 0.1, 0.9))
	add_child(cultivation_panel)

	# 面板标题
	var title = Label.new()
	title.text = "═══ 修炼地点 ═══"
	title.position = Vector2(10, 10)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	cultivation_panel.add_child(title)

func create_cultivation_buttons():
	if not game_manager:
		return

	var y_offset = 40
	var button_height = 120

	# 只显示已解锁的修炼地点
	var unlocked_spots = game_manager.get_unlocked_cultivation_spots()

	for spot_type in unlocked_spots:
		var spot_data = game_manager.cultivation_spots[spot_type]

		# 获取地点图标
		var spot_icon = get_cultivation_spot_icon(spot_type)

		# 创建地点按钮
		var button = Button.new()
		button.text = "%s %s" % [spot_icon, spot_data.name]
		button.size = Vector2(320, 60)
		button.position = Vector2(10, y_offset)
		button.add_theme_font_size_override("font_size", 14)

		# 连接按钮点击事件
		button.pressed.connect(_on_cultivation_button_pressed.bind(spot_type))

		cultivation_panel.add_child(button)
		cultivation_buttons[spot_type] = button

		# 创建成本标签
		create_cost_label(spot_data, y_offset)

		# 创建奖励标签
		create_reward_label(spot_data, y_offset)

		# 创建怪物标签
		create_monster_label(spot_data, y_offset)

		y_offset += button_height + 5

func create_cost_label(spot_data: Dictionary, y_offset: int):
	var cost_label = Label.new()
	var cost_text = "💰 消耗: "
	for resource in spot_data.cost:
		var resource_icon = get_resource_icon(resource)
		cost_text += "%s%d " % [resource_icon, spot_data.cost[resource]]
	cost_label.text = cost_text
	cost_label.position = Vector2(10, y_offset + 65)
	cost_label.add_theme_font_size_override("font_size", 11)
	cost_label.add_theme_color_override("font_color", Color.LIGHT_YELLOW)
	cultivation_panel.add_child(cost_label)

func create_reward_label(spot_data: Dictionary, y_offset: int):
	var reward_label = Label.new()
	var reward_text = "🎁 奖励: "
	for resource in spot_data.rewards:
		var resource_icon = get_resource_icon(resource)
		var amount = spot_data.rewards[resource]
		reward_text += "%s%d " % [resource_icon, amount]
	reward_label.text = reward_text
	reward_label.position = Vector2(10, y_offset + 80)
	reward_label.add_theme_font_size_override("font_size", 11)
	reward_label.add_theme_color_override("font_color", Color.LIGHT_GREEN)
	cultivation_panel.add_child(reward_label)

func create_monster_label(spot_data: Dictionary, y_offset: int):
	if spot_data.monsters.size() > 0:
		var monster_label = Label.new()
		var monster_text = "⚔️ 怪物: " + ", ".join(spot_data.monsters)
		monster_label.text = monster_text
		monster_label.position = Vector2(10, y_offset + 95)
		monster_label.add_theme_font_size_override("font_size", 10)
		monster_label.add_theme_color_override("font_color", Color.LIGHT_CORAL)
		cultivation_panel.add_child(monster_label)

func get_resource_icon(resource_key: String) -> String:
	# 获取资源图标
	match resource_key:
		"spirit_stones": return "💎"
		"qi": return "🌀"
		"sect_contribution": return "🏛️"
		"cultivation_exp": return "⭐"
		"pills": return "💊"
		"artifacts": return "⚔️"
		"techniques": return "📜"
		_: return "❓"

func get_cultivation_spot_icon(spot_type: String) -> String:
	# 获取修炼地点图标
	match spot_type:
		"meditation_room": return "🧘"
		"herb_forest": return "🌲"
		"spirit_mine": return "⛏️"
		"demon_valley": return "👹"
		"ancient_ruins": return "🏛️"
		"forbidden_land": return "💀"
		_: return "🗺️"

func _on_cultivation_button_pressed(spot_type: String):
	# 选择修炼地点
	if game_manager:
		game_manager.set_selected_cultivation_spot(spot_type)
		# 直接开始修炼
		if game_manager.start_cultivation(spot_type):
			show_message("开始修炼：%s" % game_manager.cultivation_spots[spot_type].name)
		else:
			show_message("无法开始修炼！")

func _on_resource_changed(_resource_type: String, _amount: int):
	# 更新资源显示
	update_resource_display()

func _on_realm_breakthrough(_new_realm: String):
	# 境界突破时的UI更新
	update_realm_display()
	show_message("恭喜突破到新境界！")
	# 重新创建修炼地点按钮（可能解锁新地点）
	recreate_cultivation_buttons()

func _on_cultivation_progress(progress: float):
	# 修炼进度更新
	if progress_bar:
		progress_bar.value = progress

func _on_cultivation_started(spot_type: String, spot_name: String):
	# 修炼开始时的UI反馈
	show_message("🧘 开始在 %s 修炼" % spot_name)

func _on_cultivation_completed(spot_type: String, rewards: Dictionary):
	# 修炼完成时的UI反馈
	var spot_name = game_manager.cultivation_spots[spot_type].name
	var reward_text = "完成修炼：%s\n获得奖励：" % spot_name
	for resource in rewards:
		reward_text += "\n%s %s: %d" % [get_resource_icon(resource), resource, rewards[resource]]
	show_message(reward_text)

func _on_monster_encountered(monster_name: String, location: String, victory: bool):
	# 怪物遭遇时的UI反馈
	if victory:
		show_message("⚔️ 在 %s 击败了 %s！" % [location, monster_name])
	else:
		show_message("💀 在 %s 被 %s 击败了..." % [location, monster_name])

func update_resource_display():
	# 更新所有修仙资源显示
	if not game_manager:
		return

	if spirit_stones_label:
		spirit_stones_label.text = "💎 灵石: %d" % game_manager.get_resource("spirit_stones")

	if qi_label:
		qi_label.text = "🌀 灵气: %d" % game_manager.get_resource("qi")

	if sect_contribution_label:
		sect_contribution_label.text = "🏛️ 门派贡献: %d" % game_manager.get_resource("sect_contribution")

	if cultivation_exp_label:
		cultivation_exp_label.text = "⭐ 修炼经验: %d" % game_manager.get_resource("cultivation_exp")

	if pills_label:
		pills_label.text = "💊 丹药: %d" % game_manager.get_resource("pills")

	if artifacts_label:
		artifacts_label.text = "⚔️ 法器: %d" % game_manager.get_resource("artifacts")

	if techniques_label:
		techniques_label.text = "📜 功法: %d" % game_manager.get_resource("techniques")

	# 更新境界显示
	update_realm_display()

func update_realm_display():
	# 更新境界显示
	if realm_label and game_manager:
		var realm_info = game_manager.get_current_realm_info()
		realm_label.text = "🏔️ 当前境界: %s" % realm_info.current.name

		if progress_bar:
			progress_bar.value = realm_info.progress

func recreate_cultivation_buttons():
	# 清除现有按钮
	clear_cultivation_buttons()
	# 重新创建按钮
	create_cultivation_buttons()

func clear_cultivation_buttons():
	# 清除所有修炼地点按钮和相关UI元素
	var children = cultivation_panel.get_children()
	for i in range(children.size()):
		var child = children[i]
		if i > 0:  # 保留第一个子节点（标题）
			child.queue_free()
	cultivation_buttons.clear()

func show_message(message: String):
	# 显示临时消息
	var message_label = Label.new()
	message_label.text = message
	message_label.position = Vector2(get_viewport().size.x / 2 - 150, 100)
	message_label.add_theme_font_size_override("font_size", 16)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	message_label.add_theme_color_override("font_shadow_color", Color.BLACK)
	message_label.add_theme_constant_override("shadow_offset_x", 2)
	message_label.add_theme_constant_override("shadow_offset_y", 2)
	add_child(message_label)

	# 3秒后移除消息
	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.one_shot = true
	timer.timeout.connect(func(): message_label.queue_free(); timer.queue_free())
	add_child(timer)
	timer.start()
