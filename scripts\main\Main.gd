extends Node2D

# 主场景脚本
class_name Main

# 场景组件
var game_manager: GameManager
var ui_manager: UIManager
var camera: Camera2D
var game_log: RichTextLabel

# 游戏世界
var world_size = Vector2(2000, 1500)
var grid_size = 64

# 游戏设置
var show_grid_lines = false  # 设置为true可显示网格线

func _ready():
	print("主场景启动")

	# 初始化游戏管理器
	setup_game_manager()

	# 设置摄像机
	setup_camera()

	# 创建UI
	setup_ui()

	# 创建游戏世界
	setup_world()

	print("游戏初始化完成")

func setup_game_manager():
	# 创建游戏管理器实例
	game_manager = GameManager.new()
	game_manager.name = "GameManager"
	add_child(game_manager)

	# 连接信号
	game_manager.building_placed.connect(_on_building_placed)
	game_manager.realm_breakthrough.connect(_on_realm_breakthrough)
	game_manager.resource_changed.connect(_on_resource_changed)

func setup_camera():
	# 获取场景中的摄像机
	camera = get_node("Camera2D")

	# 设置摄像机限制
	var limit_left = 0
	var limit_top = 0
	var limit_right = world_size.x
	var limit_bottom = world_size.y

	camera.limit_left = limit_left
	camera.limit_top = limit_top
	camera.limit_right = limit_right
	camera.limit_bottom = limit_bottom

func setup_ui():
	# 获取场景中的UI管理器
	ui_manager = get_node("UILayer/UIManager")

	# 获取游戏日志
	game_log = get_node("UILayer/GameLog/LogText")

	# 添加欢迎消息
	add_log_message("🌟 修仙之路开始了！", Color.GOLD)
	add_log_message("🏔️ 你是一名刚入门的修炼者", Color.CYAN)
	add_log_message("�️ 选择修炼地点开始历练", Color.LIGHT_BLUE)
	add_log_message("⚔️ 小心！野外可能遇到妖兽", Color.ORANGE)

func setup_world():
	# 背景已在场景中创建
	# 根据设置决定是否显示网格线
	if show_grid_lines:
		create_grid_lines()

func create_background():
	# 背景已在场景文件中创建，这个函数保留用于兼容性
	pass

func create_grid_lines():
	# 创建网格线用于调试
	var grid_lines = Node2D.new()
	grid_lines.name = "GridLines"
	add_child(grid_lines)

	# 垂直线
	for x in range(0, world_size.x + 1, grid_size):
		var line = Line2D.new()
		line.add_point(Vector2(x, 0))
		line.add_point(Vector2(x, world_size.y))
		line.default_color = Color(1, 1, 1, 0.2)
		line.width = 1
		grid_lines.add_child(line)

	# 水平线
	for y in range(0, world_size.y + 1, grid_size):
		var line = Line2D.new()
		line.add_point(Vector2(0, y))
		line.add_point(Vector2(world_size.x, y))
		line.default_color = Color(1, 1, 1, 0.2)
		line.width = 1
		grid_lines.add_child(line)

func add_log_message(message: String, color: Color = Color.WHITE):
	# 添加日志消息
	if game_log:
		# 使用更简单的时间戳方式
		var time_dict = Time.get_datetime_dict_from_system()
		var timestamp = "%02d:%02d:%02d" % [time_dict.hour, time_dict.minute, time_dict.second]

		var formatted_message = "[color=#%s][%s] %s[/color]" % [color.to_html(), timestamp, message]
		game_log.append_text(formatted_message + "\n")

func _on_cultivation_started(spot_type: String, spot_name: String):
	# 当开始修炼时调用
	print("主场景收到修炼开始信号：%s" % spot_name)

	# 添加日志消息
	var spot_icon = get_cultivation_spot_icon(spot_type)
	add_log_message("🧘 前往 %s %s 修炼" % [spot_icon, spot_name], Color.GREEN)

func _on_cultivation_completed(spot_type: String, rewards: Dictionary):
	# 当修炼完成时调用
	var spot_data = game_manager.cultivation_spots[spot_type]
	var spot_icon = get_cultivation_spot_icon(spot_type)
	add_log_message("✅ 完成 %s %s 的修炼" % [spot_icon, spot_data.name], Color.LIGHT_GREEN)

	# 记录获得的奖励
	var reward_text = "获得奖励："
	for resource in rewards:
		reward_text += " %s+%d" % [resource, rewards[resource]]
	add_log_message("🎁 %s" % reward_text, Color.YELLOW)

func _on_monster_encountered(monster_name: String, location: String, victory: bool):
	# 当遭遇怪物时调用
	if victory:
		add_log_message("⚔️ 在%s击败了%s！" % [location, monster_name], Color.ORANGE)
	else:
		add_log_message("💀 在%s被%s击败了..." % [location, monster_name], Color.RED)

func _on_realm_breakthrough(new_realm: String):
	# 境界突破时调用
	var realm_data = game_manager.cultivation_realms[new_realm]
	add_log_message("🌟 恭喜！突破到 %s 境界！" % realm_data.name, Color.GOLD)
	add_log_message("🔓 解锁了新的修炼地点", Color.CYAN)

func _on_resource_changed(resource_type: String, amount: int):
	# 资源变化时调用（只记录重要的资源变化）
	if resource_type == "cultivation_exp":
		# 每获得50修炼经验记录一次
		if amount > 0 and amount % 50 == 0:
			add_log_message("⭐ 修炼经验达到 %d" % amount, Color.YELLOW)

func get_cultivation_spot_icon(spot_type: String) -> String:
	# 获取修炼地点图标
	match spot_type:
		"meditation_room": return "🧘"
		"herb_forest": return "🌲"
		"spirit_mine": return "⛏️"
		"demon_valley": return "�"
		"ancient_ruins": return "🏛️"
		"forbidden_land": return "�"
		_: return "🗺️"

func create_building_visual(building_type: String, building_position: Vector2):
	# 创建建筑的视觉节点
	var building_visual = Building.new()
	building_visual.name = "Building_%s_%d" % [building_type, get_children().size()]

	# 初始化建筑，传递GameManager引用
	var building_data = game_manager.building_types[building_type]
	building_visual.initialize(building_type, building_data, building_position, game_manager)

	add_child(building_visual)

func _input(event):
	# 处理摄像机移动
	handle_camera_movement(event)

func handle_camera_movement(event):
	# 使用WASD或箭头键移动摄像机
	var camera_speed = 300
	var move_vector = Vector2.ZERO

	if Input.is_action_pressed("ui_left"):
		move_vector.x -= 1
	if Input.is_action_pressed("ui_right"):
		move_vector.x += 1
	if Input.is_action_pressed("ui_up"):
		move_vector.y -= 1
	if Input.is_action_pressed("ui_down"):
		move_vector.y += 1

	if move_vector != Vector2.ZERO:
		camera.position += move_vector.normalized() * camera_speed * get_process_delta_time()

		# 限制摄像机位置
		camera.position.x = clamp(camera.position.x, 640, world_size.x - 640)
		camera.position.y = clamp(camera.position.y, 360, world_size.y - 360)

	# 处理缩放
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			camera.zoom *= 1.1
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			camera.zoom *= 0.9

		# 限制缩放范围
		camera.zoom.x = clamp(camera.zoom.x, 0.5, 2.0)
		camera.zoom.y = clamp(camera.zoom.y, 0.5, 2.0)

func _process(_delta):
	# 每帧更新
	pass

func pause_game():
	# 暂停游戏
	if game_manager:
		game_manager.pause_game()

func resume_game():
	# 继续游戏
	if game_manager:
		game_manager.resume_game()

func save_game():
	# 保存游戏
	if game_manager:
		game_manager.save_game()

func load_game():
	# 加载游戏
	if game_manager:
		game_manager.load_game()
