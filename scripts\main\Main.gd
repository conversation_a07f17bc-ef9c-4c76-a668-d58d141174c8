extends Node2D

# 主场景脚本
class_name Main

# 场景组件
var game_manager: GameManager
var ui_manager: UIManager
var camera: Camera2D

# 游戏世界
var world_size = Vector2(2000, 1500)
var grid_size = 64

func _ready():
	print("主场景启动")

	# 初始化游戏管理器
	setup_game_manager()

	# 设置摄像机
	setup_camera()

	# 创建UI
	setup_ui()

	# 创建游戏世界
	setup_world()

	print("游戏初始化完成")

func setup_game_manager():
	# 创建游戏管理器实例
	game_manager = GameManager.new()
	game_manager.name = "GameManager"
	add_child(game_manager)

	# 连接信号
	game_manager.building_placed.connect(_on_building_placed)

func setup_camera():
	# 创建摄像机
	camera = Camera2D.new()
	camera.position = Vector2(640, 360)  # 屏幕中心
	camera.enabled = true
	add_child(camera)

	# 设置摄像机限制
	var limit_left = 0
	var limit_top = 0
	var limit_right = world_size.x
	var limit_bottom = world_size.y

	camera.limit_left = limit_left
	camera.limit_top = limit_top
	camera.limit_right = limit_right
	camera.limit_bottom = limit_bottom

func setup_ui():
	# 创建UI管理器
	ui_manager = UIManager.new()
	ui_manager.name = "UIManager"
	add_child(ui_manager)

func setup_world():
	# 创建游戏世界背景
	create_background()

	# 创建网格线（可选，用于调试）
	if OS.is_debug_build():
		create_grid_lines()

func create_background():
	# 创建简单的背景
	var background = ColorRect.new()
	background.color = Color(0.2, 0.6, 0.2)  # 绿色背景
	background.size = world_size
	background.z_index = -100
	add_child(background)

func create_grid_lines():
	# 创建网格线用于调试
	var grid_lines = Node2D.new()
	grid_lines.name = "GridLines"
	add_child(grid_lines)

	# 垂直线
	for x in range(0, world_size.x + 1, grid_size):
		var line = Line2D.new()
		line.add_point(Vector2(x, 0))
		line.add_point(Vector2(x, world_size.y))
		line.default_color = Color(1, 1, 1, 0.2)
		line.width = 1
		grid_lines.add_child(line)

	# 水平线
	for y in range(0, world_size.y + 1, grid_size):
		var line = Line2D.new()
		line.add_point(Vector2(0, y))
		line.add_point(Vector2(world_size.x, y))
		line.default_color = Color(1, 1, 1, 0.2)
		line.width = 1
		grid_lines.add_child(line)

func _on_building_placed(building_type: String, building_position: Vector2):
	# 当建筑被放置时调用
	print("主场景收到建筑放置信号：%s 在 %s" % [building_type, building_position])

	# 创建建筑的视觉表示
	create_building_visual(building_type, building_position)

func create_building_visual(building_type: String, building_position: Vector2):
	# 创建建筑的视觉节点
	var building_visual = Building.new()
	building_visual.name = "Building_%s_%d" % [building_type, get_children().size()]

	# 初始化建筑
	var building_data = game_manager.building_types[building_type]
	building_visual.initialize(building_type, building_data, building_position)

	add_child(building_visual)

func _input(event):
	# 处理摄像机移动
	handle_camera_movement(event)

func handle_camera_movement(event):
	# 使用WASD或箭头键移动摄像机
	var camera_speed = 300
	var move_vector = Vector2.ZERO

	if Input.is_action_pressed("ui_left"):
		move_vector.x -= 1
	if Input.is_action_pressed("ui_right"):
		move_vector.x += 1
	if Input.is_action_pressed("ui_up"):
		move_vector.y -= 1
	if Input.is_action_pressed("ui_down"):
		move_vector.y += 1

	if move_vector != Vector2.ZERO:
		camera.position += move_vector.normalized() * camera_speed * get_process_delta_time()

		# 限制摄像机位置
		camera.position.x = clamp(camera.position.x, 640, world_size.x - 640)
		camera.position.y = clamp(camera.position.y, 360, world_size.y - 360)

	# 处理缩放
	if event is InputEventMouseButton:
		if event.button_index == MOUSE_BUTTON_WHEEL_UP:
			camera.zoom *= 1.1
		elif event.button_index == MOUSE_BUTTON_WHEEL_DOWN:
			camera.zoom *= 0.9

		# 限制缩放范围
		camera.zoom.x = clamp(camera.zoom.x, 0.5, 2.0)
		camera.zoom.y = clamp(camera.zoom.y, 0.5, 2.0)

func _process(_delta):
	# 每帧更新
	pass

func pause_game():
	# 暂停游戏
	if game_manager:
		game_manager.pause_game()

func resume_game():
	# 继续游戏
	if game_manager:
		game_manager.resume_game()

func save_game():
	# 保存游戏
	if game_manager:
		game_manager.save_game()

func load_game():
	# 加载游戏
	if game_manager:
		game_manager.load_game()
