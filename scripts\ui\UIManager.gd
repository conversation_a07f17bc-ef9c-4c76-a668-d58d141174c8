extends Control

# 修仙UI管理器 - 个人修炼者视角
class_name UIManager

# UI元素引用
@onready var resource_panel: Panel
@onready var activity_panel: Panel
@onready var realm_panel: Panel
@onready var spirit_stones_label: Label
@onready var qi_label: Label
@onready var sect_contribution_label: Label
@onready var cultivation_exp_label: Label
@onready var pills_label: Label
@onready var artifacts_label: Label
@onready var techniques_label: Label
@onready var realm_label: Label
@onready var progress_bar: ProgressBar

# 建筑按钮
var building_buttons = {}

# 游戏管理器引用
var game_manager: GameManager

func _ready():
	# 延迟获取游戏管理器引用，等待Main场景完全初始化
	call_deferred("setup_game_manager_connection")

	# 创建UI元素
	setup_ui()

func setup_game_manager_connection():
	# 获取游戏管理器引用
	game_manager = get_node("../../GameManager")

	# 连接信号
	if game_manager:
		game_manager.resource_changed.connect(_on_resource_changed)
		game_manager.realm_breakthrough.connect(_on_realm_breakthrough)
		game_manager.cultivation_progress.connect(_on_cultivation_progress)
		game_manager.cultivation_started.connect(_on_cultivation_started)
		game_manager.cultivation_completed.connect(_on_cultivation_completed)
		game_manager.monster_encountered.connect(_on_monster_encountered)

		# 初始化资源显示
		update_resource_display()

		# 创建修炼地点按钮
		create_building_buttons()
	else:
		print("警告：无法找到GameManager")

func setup_ui():
	# 创建资源显示面板
	create_resource_panel()

	# 创建境界显示面板
	create_realm_panel()

	# 创建建筑选择面板
	create_building_panel()

func create_resource_panel():
	# 修仙资源面板 - 纯文字版本
	resource_panel = Panel.new()
	resource_panel.size = Vector2(500, 250)
	resource_panel.position = Vector2(10, 10)
	resource_panel.add_theme_color_override("bg_color", Color(0.1, 0.1, 0.2, 0.9))
	add_child(resource_panel)

	# 标题
	var title = Label.new()
	title.text = "═══ 修仙资源 ═══"
	title.position = Vector2(10, 5)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	resource_panel.add_child(title)

	# 灵石标签
	spirit_stones_label = Label.new()
	spirit_stones_label.text = "💎 灵石: 100"
	spirit_stones_label.position = Vector2(10, 35)
	spirit_stones_label.add_theme_font_size_override("font_size", 14)
	spirit_stones_label.add_theme_color_override("font_color", Color.CYAN)
	resource_panel.add_child(spirit_stones_label)

	# 灵气标签
	qi_label = Label.new()
	qi_label.text = "🌀 灵气: 50"
	qi_label.position = Vector2(10, 60)
	qi_label.add_theme_font_size_override("font_size", 14)
	qi_label.add_theme_color_override("font_color", Color.LIGHT_BLUE)
	resource_panel.add_child(qi_label)

	# 门派贡献标签
	sect_contribution_label = Label.new()
	sect_contribution_label.text = "🏛️ 门派贡献: 0"
	sect_contribution_label.position = Vector2(10, 85)
	sect_contribution_label.add_theme_font_size_override("font_size", 14)
	sect_contribution_label.add_theme_color_override("font_color", Color.LIGHT_GREEN)
	resource_panel.add_child(sect_contribution_label)

	# 修炼经验标签
	cultivation_exp_label = Label.new()
	cultivation_exp_label.text = "⭐ 修炼经验: 0"
	cultivation_exp_label.position = Vector2(10, 110)
	cultivation_exp_label.add_theme_font_size_override("font_size", 14)
	cultivation_exp_label.add_theme_color_override("font_color", Color.YELLOW)
	resource_panel.add_child(cultivation_exp_label)

	# 丹药标签
	pills_label = Label.new()
	pills_label.text = "💊 丹药: 0"
	pills_label.position = Vector2(250, 35)
	pills_label.add_theme_font_size_override("font_size", 14)
	pills_label.add_theme_color_override("font_color", Color.ORANGE)
	resource_panel.add_child(pills_label)

	# 法器标签
	artifacts_label = Label.new()
	artifacts_label.text = "⚔️ 法器: 0"
	artifacts_label.position = Vector2(250, 60)
	artifacts_label.add_theme_font_size_override("font_size", 14)
	artifacts_label.add_theme_color_override("font_color", Color.MAGENTA)
	resource_panel.add_child(artifacts_label)

	# 功法标签
	techniques_label = Label.new()
	techniques_label.text = "📜 功法: 0"
	techniques_label.position = Vector2(250, 85)
	techniques_label.add_theme_font_size_override("font_size", 14)
	techniques_label.add_theme_color_override("font_color", Color.LIGHT_CORAL)
	resource_panel.add_child(techniques_label)

func create_realm_panel():
	# 境界显示面板 - 纯文字版本
	realm_panel = Panel.new()
	realm_panel.size = Vector2(500, 120)
	realm_panel.position = Vector2(10, 270)
	realm_panel.add_theme_color_override("bg_color", Color(0.2, 0.1, 0.1, 0.9))
	add_child(realm_panel)

	# 境界标题
	var title = Label.new()
	title.text = "═══ 修炼境界 ═══"
	title.position = Vector2(10, 5)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	realm_panel.add_child(title)

	# 当前境界标签
	realm_label = Label.new()
	realm_label.text = "🏔️ 当前境界: 凡人"
	realm_label.position = Vector2(10, 35)
	realm_label.add_theme_font_size_override("font_size", 16)
	realm_label.add_theme_color_override("font_color", Color.LIGHT_CYAN)
	realm_panel.add_child(realm_label)

	# 修炼进度条（文字版）
	progress_bar = ProgressBar.new()
	progress_bar.size = Vector2(400, 25)
	progress_bar.position = Vector2(10, 65)
	progress_bar.min_value = 0.0
	progress_bar.max_value = 1.0
	progress_bar.value = 0.0
	progress_bar.add_theme_color_override("fill", Color.GOLD)
	progress_bar.add_theme_color_override("background", Color(0.3, 0.3, 0.3))
	realm_panel.add_child(progress_bar)

	# 进度标签
	var progress_label = Label.new()
	progress_label.text = "⚡ 突破进度"
	progress_label.position = Vector2(420, 65)
	progress_label.add_theme_font_size_override("font_size", 12)
	progress_label.add_theme_color_override("font_color", Color.YELLOW)
	realm_panel.add_child(progress_label)

func create_building_panel():
	# 修炼活动面板 - 纯文字版本
	activity_panel = Panel.new()
	activity_panel.size = Vector2(350, 500)
	activity_panel.position = Vector2(get_viewport().size.x - 360, 10)
	activity_panel.add_theme_color_override("bg_color", Color(0.1, 0.2, 0.1, 0.9))
	add_child(activity_panel)

	# 面板标题
	var title = Label.new()
	title.text = "═══ 修炼活动 ═══"
	title.position = Vector2(10, 10)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	activity_panel.add_child(title)

	# 建筑按钮将在GameManager连接后创建

func create_building_buttons():
	var y_offset = 40
	var button_height = 120

	# 只显示已解锁的修炼地点
	var unlocked_spots = game_manager.get_unlocked_cultivation_spots()

	for spot_type in unlocked_spots:
		var spot_data = game_manager.cultivation_spots[spot_type]

		# 获取地点图标
		var spot_icon = get_cultivation_spot_icon(spot_type)

		# 创建地点按钮
		var button = Button.new()
		button.text = "%s %s" % [spot_icon, spot_data.name]
		button.size = Vector2(320, 60)
		button.position = Vector2(10, y_offset)
		button.add_theme_font_size_override("font_size", 14)

		# 连接按钮点击事件
		button.pressed.connect(_on_cultivation_button_pressed.bind(spot_type))

		activity_panel.add_child(button)
		building_buttons[spot_type] = button

		# 创建成本标签
		var cost_label = Label.new()
		var cost_text = "💰 消耗: "
		for resource in spot_data.cost:
			var resource_icon = get_resource_icon(resource)
			cost_text += "%s%d " % [resource_icon, spot_data.cost[resource]]
		cost_label.text = cost_text
		cost_label.position = Vector2(10, y_offset + 65)
		cost_label.add_theme_font_size_override("font_size", 11)
		cost_label.add_theme_color_override("font_color", Color.LIGHT_YELLOW)
		activity_panel.add_child(cost_label)

		# 创建奖励标签
		var reward_label = Label.new()
		var reward_text = "🎁 奖励: "
		for resource in spot_data.rewards:
			var resource_icon = get_resource_icon(resource)
			var amount = spot_data.rewards[resource]
			reward_text += "%s%d " % [resource_icon, amount]
		reward_label.text = reward_text
		reward_label.position = Vector2(10, y_offset + 80)
		reward_label.add_theme_font_size_override("font_size", 11)
		reward_label.add_theme_color_override("font_color", Color.LIGHT_GREEN)
		activity_panel.add_child(reward_label)

		# 创建怪物标签
		if spot_data.monsters.size() > 0:
			var monster_label = Label.new()
			var monster_text = "⚔️ 怪物: " + ", ".join(spot_data.monsters)
			monster_label.text = monster_text
			monster_label.position = Vector2(10, y_offset + 95)
			monster_label.add_theme_font_size_override("font_size", 10)
			monster_label.add_theme_color_override("font_color", Color.LIGHT_CORAL)
			activity_panel.add_child(monster_label)

		y_offset += button_height + 5

func recreate_building_buttons():
	# 清除现有按钮
	clear_building_buttons()
	# 重新创建按钮
	create_building_buttons()

func clear_building_buttons():
	# 清除所有活动按钮和相关UI元素
	var children = activity_panel.get_children()
	for i in range(children.size()):
		var child = children[i]
		if i > 0:  # 保留第一个子节点（标题）
			child.queue_free()
	building_buttons.clear()

func get_resource_display_name(resource_key: String) -> String:
	# 将资源键转换为显示名称
	match resource_key:
		"spirit_stones": return "灵石"
		"qi": return "灵气"
		"disciples": return "弟子"
		"cultivation_exp": return "修炼经验"
		"pills": return "丹药"
		"artifacts": return "法器"
		"merit": return "功德"
		_: return resource_key

func get_resource_icon(resource_key: String) -> String:
	# 获取资源图标
	match resource_key:
		"spirit_stones": return "💎"
		"qi": return "🌀"
		"sect_contribution": return "🏛️"
		"cultivation_exp": return "⭐"
		"pills": return "💊"
		"artifacts": return "⚔️"
		"techniques": return "📜"
		_: return "❓"

func get_building_icon(building_type: String) -> String:
	# 获取修炼活动图标
	match building_type:
		"meditation_spot": return "🧘"
		"herb_gathering": return "🌿"
		"sect_mission": return "📋"
		"spirit_mining": return "⛏️"
		"treasure_hunting": return "🗺️"
		"technique_study": return "📚"
		"artifact_refining": return "⚒️"
		"inner_alchemy": return "🔮"
		_: return "🏗️"

func get_cultivation_spot_icon(spot_type: String) -> String:
	# 获取修炼地点图标
	match spot_type:
		"meditation_room": return "🧘"
		"herb_forest": return "🌲"
		"spirit_mine": return "⛏️"
		"demon_valley": return "👹"
		"ancient_ruins": return "🏛️"
		"forbidden_land": return "💀"
		_: return "🗺️"

func _on_building_button_pressed(building_type: String):
	# 选择建筑类型（保留兼容性）
	if game_manager.has_method("set_selected_building_type"):
		game_manager.set_selected_building_type(building_type)

	# 更新按钮状态
	update_building_button_states(building_type)

	print("选择了建筑：", building_type)

func _on_cultivation_button_pressed(spot_type: String):
	# 选择修炼地点并直接开始修炼
	if game_manager:
		game_manager.set_selected_cultivation_spot(spot_type)
		# 直接开始修炼
		if game_manager.start_cultivation(spot_type):
			show_message("开始修炼：%s" % game_manager.cultivation_spots[spot_type].name)
		else:
			show_message("无法开始修炼！")

func update_building_button_states(selected_type: String):
	# 更新建筑按钮的视觉状态
	for building_type in building_buttons:
		var button = building_buttons[building_type]
		if building_type == selected_type:
			button.modulate = Color.GREEN
		else:
			button.modulate = Color.WHITE

func _on_resource_changed(_resource_type: String, _amount: int):
	# 更新资源显示
	update_resource_display()

func _on_realm_breakthrough(_new_realm: String):
	# 境界突破时的UI更新
	update_realm_display()
	show_message("恭喜突破到新境界！")
	# 重新创建建筑按钮（可能解锁新建筑）
	recreate_building_buttons()

func _on_cultivation_progress(progress: float):
	# 修炼进度更新
	if progress_bar:
		progress_bar.value = progress

func _on_cultivation_started(_spot_type: String, spot_name: String):
	# 修炼开始时的UI反馈
	show_message("🧘 开始在 %s 修炼" % spot_name)

func _on_cultivation_completed(_spot_type: String, rewards: Dictionary):
	# 修炼完成时的UI反馈
	var reward_text = "修炼完成！获得奖励："
	for resource in rewards:
		reward_text += " %s+%d" % [resource, rewards[resource]]
	show_message(reward_text)

func _on_monster_encountered(monster_name: String, location: String, victory: bool):
	# 怪物遭遇时的UI反馈
	if victory:
		show_message("⚔️ 在%s击败了%s！" % [location, monster_name])
	else:
		show_message("💀 在%s被%s击败了..." % [location, monster_name])

func update_resource_display():
	# 更新所有修仙资源显示
	if spirit_stones_label:
		spirit_stones_label.text = "💎 灵石: %d" % game_manager.get_resource("spirit_stones")

	if qi_label:
		qi_label.text = "🌀 灵气: %d" % game_manager.get_resource("qi")

	if sect_contribution_label:
		sect_contribution_label.text = "🏛️ 门派贡献: %d" % game_manager.get_resource("sect_contribution")

	if cultivation_exp_label:
		cultivation_exp_label.text = "⭐ 修炼经验: %d" % game_manager.get_resource("cultivation_exp")

	if pills_label:
		pills_label.text = "💊 丹药: %d" % game_manager.get_resource("pills")

	if artifacts_label:
		artifacts_label.text = "⚔️ 法器: %d" % game_manager.get_resource("artifacts")

	if techniques_label:
		techniques_label.text = "📜 功法: %d" % game_manager.get_resource("techniques")

	# 更新境界显示
	update_realm_display()

	# 更新建筑按钮的可用状态
	update_building_button_availability()

func update_realm_display():
	# 更新境界显示
	if realm_label and game_manager:
		var realm_info = game_manager.get_current_realm_info()
		realm_label.text = "当前境界: %s" % realm_info.current.name

		if progress_bar:
			progress_bar.value = realm_info.progress

func update_building_button_availability():
	# 根据资源情况更新修炼地点按钮的可用性
	for spot_type in building_buttons:
		var button = building_buttons[spot_type]
		var spot_data = game_manager.cultivation_spots[spot_type]

		if game_manager.can_afford(spot_data.cost):
			button.disabled = false
			button.modulate.a = 1.0
		else:
			button.disabled = true
			button.modulate.a = 0.5

func show_message(message: String):
	# 显示临时消息
	var message_label = Label.new()
	message_label.text = message
	message_label.position = Vector2(get_viewport().size.x / 2 - 100, 100)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(message_label)

	# 3秒后移除消息
	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.one_shot = true
	timer.timeout.connect(func(): message_label.queue_free(); timer.queue_free())
	add_child(timer)
	timer.start()

func _input(event):
	# 处理输入事件
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		# 检查是否在游戏区域点击（不在UI面板上）
		if not is_point_in_ui_panels(event.position):
			handle_game_area_click(event.position)

func is_point_in_ui_panels(point: Vector2) -> bool:
	# 检查点击位置是否在UI面板内
	var resource_rect = Rect2(resource_panel.position, resource_panel.size)
	var activity_rect = Rect2(activity_panel.position, activity_panel.size)

	return resource_rect.has_point(point) or activity_rect.has_point(point)

func handle_game_area_click(_click_position: Vector2):
	# 处理游戏区域的点击（修炼地点系统不需要点击放置）
	# 修炼地点通过按钮直接选择
	show_message("请点击右侧修炼地点按钮开始修炼")

func snap_to_grid(click_position: Vector2) -> Vector2:
	# 将位置对齐到网格
	var grid_size = 64.0
	var x = int(click_position.x / grid_size) * grid_size + grid_size / 2.0
	var y = int(click_position.y / grid_size) * grid_size + grid_size / 2.0
	return Vector2(x, y)
