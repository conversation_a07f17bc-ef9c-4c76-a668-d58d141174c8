extends Control

# UI管理器 - 负责管理游戏界面
class_name UIManager

# UI元素引用
@onready var resource_panel: Panel
@onready var building_panel: Panel
@onready var coins_label: Label
@onready var energy_label: Label
@onready var population_label: Label

# 建筑按钮
var building_buttons = {}

# 游戏管理器引用
var game_manager: GameManager

func _ready():
	# 获取游戏管理器引用
	game_manager = get_node("/root/GameManager")
	
	# 连接信号
	if game_manager:
		game_manager.resource_changed.connect(_on_resource_changed)
	
	# 创建UI元素
	setup_ui()
	
	# 初始化资源显示
	update_resource_display()

func setup_ui():
	# 创建资源显示面板
	create_resource_panel()
	
	# 创建建筑选择面板
	create_building_panel()

func create_resource_panel():
	# 资源面板
	resource_panel = Panel.new()
	resource_panel.size = Vector2(300, 100)
	resource_panel.position = Vector2(10, 10)
	add_child(resource_panel)
	
	# 金币标签
	coins_label = Label.new()
	coins_label.text = "金币: 100"
	coins_label.position = Vector2(10, 10)
	resource_panel.add_child(coins_label)
	
	# 能量标签
	energy_label = Label.new()
	energy_label.text = "能量: 50"
	energy_label.position = Vector2(10, 35)
	resource_panel.add_child(energy_label)
	
	# 人口标签
	population_label = Label.new()
	population_label.text = "人口: 0"
	population_label.position = Vector2(10, 60)
	resource_panel.add_child(population_label)

func create_building_panel():
	# 建筑选择面板
	building_panel = Panel.new()
	building_panel.size = Vector2(200, 400)
	building_panel.position = Vector2(get_viewport().size.x - 210, 10)
	add_child(building_panel)
	
	# 面板标题
	var title = Label.new()
	title.text = "建筑选择"
	title.position = Vector2(10, 10)
	building_panel.add_child(title)
	
	# 创建建筑按钮
	create_building_buttons()

func create_building_buttons():
	var y_offset = 40
	var button_height = 60
	
	for building_type in game_manager.building_types:
		var building_data = game_manager.building_types[building_type]
		
		# 创建建筑按钮
		var button = Button.new()
		button.text = building_data.name
		button.size = Vector2(180, 50)
		button.position = Vector2(10, y_offset)
		
		# 连接按钮点击事件
		button.pressed.connect(_on_building_button_pressed.bind(building_type))
		
		building_panel.add_child(button)
		building_buttons[building_type] = button
		
		# 创建成本标签
		var cost_label = Label.new()
		var cost_text = "成本: "
		for resource in building_data.cost:
			cost_text += "%s:%d " % [resource, building_data.cost[resource]]
		cost_label.text = cost_text
		cost_label.position = Vector2(10, y_offset + 25)
		cost_label.add_theme_font_size_override("font_size", 12)
		building_panel.add_child(cost_label)
		
		y_offset += button_height + 10

func _on_building_button_pressed(building_type: String):
	# 选择建筑类型
	game_manager.set_selected_building_type(building_type)
	
	# 更新按钮状态
	update_building_button_states(building_type)
	
	print("选择了建筑：", building_type)

func update_building_button_states(selected_type: String):
	# 更新建筑按钮的视觉状态
	for building_type in building_buttons:
		var button = building_buttons[building_type]
		if building_type == selected_type:
			button.modulate = Color.GREEN
		else:
			button.modulate = Color.WHITE

func _on_resource_changed(resource_type: String, amount: int):
	# 更新资源显示
	update_resource_display()

func update_resource_display():
	# 更新所有资源显示
	if coins_label:
		coins_label.text = "金币: %d" % game_manager.get_resource("coins")
	
	if energy_label:
		energy_label.text = "能量: %d" % game_manager.get_resource("energy")
	
	if population_label:
		population_label.text = "人口: %d" % game_manager.get_resource("population")
	
	# 更新建筑按钮的可用状态
	update_building_button_availability()

func update_building_button_availability():
	# 根据资源情况更新建筑按钮的可用性
	for building_type in building_buttons:
		var button = building_buttons[building_type]
		var building_data = game_manager.building_types[building_type]
		
		if game_manager.can_afford(building_data.cost):
			button.disabled = false
			button.modulate.a = 1.0
		else:
			button.disabled = true
			button.modulate.a = 0.5

func show_message(message: String):
	# 显示临时消息
	var message_label = Label.new()
	message_label.text = message
	message_label.position = Vector2(get_viewport().size.x / 2 - 100, 100)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(message_label)
	
	# 3秒后移除消息
	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.one_shot = true
	timer.timeout.connect(func(): message_label.queue_free(); timer.queue_free())
	add_child(timer)
	timer.start()

func _input(event):
	# 处理输入事件
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		# 检查是否在游戏区域点击（不在UI面板上）
		if not is_point_in_ui_panels(event.position):
			handle_game_area_click(event.position)

func is_point_in_ui_panels(point: Vector2) -> bool:
	# 检查点击位置是否在UI面板内
	var resource_rect = Rect2(resource_panel.position, resource_panel.size)
	var building_rect = Rect2(building_panel.position, building_panel.size)
	
	return resource_rect.has_point(point) or building_rect.has_point(point)

func handle_game_area_click(position: Vector2):
	# 处理游戏区域的点击
	if game_manager.selected_building_type != "":
		# 尝试在点击位置放置建筑
		var grid_position = snap_to_grid(position)
		
		if game_manager.place_building(game_manager.selected_building_type, grid_position):
			show_message("建筑放置成功！")
		else:
			show_message("无法放置建筑！")

func snap_to_grid(position: Vector2) -> Vector2:
	# 将位置对齐到网格
	var grid_size = 64
	var x = int(position.x / grid_size) * grid_size + grid_size / 2
	var y = int(position.y / grid_size) * grid_size + grid_size / 2
	return Vector2(x, y)
