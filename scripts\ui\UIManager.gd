extends Control

# 修仙UI管理器 - 负责管理修仙游戏界面
class_name UIManager

# UI元素引用
@onready var resource_panel: Panel
@onready var building_panel: Panel
@onready var realm_panel: Panel
@onready var spirit_stones_label: Label
@onready var qi_label: Label
@onready var disciples_label: Label
@onready var cultivation_exp_label: Label
@onready var pills_label: Label
@onready var artifacts_label: Label
@onready var merit_label: Label
@onready var realm_label: Label
@onready var progress_bar: ProgressBar

# 建筑按钮
var building_buttons = {}

# 游戏管理器引用
var game_manager: GameManager

func _ready():
	# 延迟获取游戏管理器引用，等待Main场景完全初始化
	call_deferred("setup_game_manager_connection")

	# 创建UI元素
	setup_ui()

func setup_game_manager_connection():
	# 获取游戏管理器引用
	game_manager = get_node("../../GameManager")

	# 连接信号
	if game_manager:
		game_manager.resource_changed.connect(_on_resource_changed)
		game_manager.realm_breakthrough.connect(_on_realm_breakthrough)
		game_manager.cultivation_progress.connect(_on_cultivation_progress)

		# 初始化资源显示
		update_resource_display()

		# 创建建筑按钮
		create_building_buttons()
	else:
		print("警告：无法找到GameManager")

func setup_ui():
	# 创建资源显示面板
	create_resource_panel()

	# 创建境界显示面板
	create_realm_panel()

	# 创建建筑选择面板
	create_building_panel()

func create_resource_panel():
	# 修仙资源面板 - 纯文字版本
	resource_panel = Panel.new()
	resource_panel.size = Vector2(500, 250)
	resource_panel.position = Vector2(10, 10)
	resource_panel.add_theme_color_override("bg_color", Color(0.1, 0.1, 0.2, 0.9))
	add_child(resource_panel)

	# 标题
	var title = Label.new()
	title.text = "═══ 修仙资源 ═══"
	title.position = Vector2(10, 5)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	resource_panel.add_child(title)

	# 灵石标签
	spirit_stones_label = Label.new()
	spirit_stones_label.text = "💎 灵石: 100"
	spirit_stones_label.position = Vector2(10, 35)
	spirit_stones_label.add_theme_font_size_override("font_size", 14)
	spirit_stones_label.add_theme_color_override("font_color", Color.CYAN)
	resource_panel.add_child(spirit_stones_label)

	# 灵气标签
	qi_label = Label.new()
	qi_label.text = "🌀 灵气: 50"
	qi_label.position = Vector2(10, 60)
	qi_label.add_theme_font_size_override("font_size", 14)
	qi_label.add_theme_color_override("font_color", Color.LIGHT_BLUE)
	resource_panel.add_child(qi_label)

	# 弟子标签
	disciples_label = Label.new()
	disciples_label.text = "👥 弟子: 0"
	disciples_label.position = Vector2(10, 85)
	disciples_label.add_theme_font_size_override("font_size", 14)
	disciples_label.add_theme_color_override("font_color", Color.LIGHT_GREEN)
	resource_panel.add_child(disciples_label)

	# 修炼经验标签
	cultivation_exp_label = Label.new()
	cultivation_exp_label.text = "⭐ 修炼经验: 0"
	cultivation_exp_label.position = Vector2(10, 110)
	cultivation_exp_label.add_theme_font_size_override("font_size", 14)
	cultivation_exp_label.add_theme_color_override("font_color", Color.YELLOW)
	resource_panel.add_child(cultivation_exp_label)

	# 丹药标签
	pills_label = Label.new()
	pills_label.text = "💊 丹药: 0"
	pills_label.position = Vector2(250, 35)
	pills_label.add_theme_font_size_override("font_size", 14)
	pills_label.add_theme_color_override("font_color", Color.ORANGE)
	resource_panel.add_child(pills_label)

	# 法器标签
	artifacts_label = Label.new()
	artifacts_label.text = "⚔️ 法器: 0"
	artifacts_label.position = Vector2(250, 60)
	artifacts_label.add_theme_font_size_override("font_size", 14)
	artifacts_label.add_theme_color_override("font_color", Color.MAGENTA)
	resource_panel.add_child(artifacts_label)

	# 功德标签
	merit_label = Label.new()
	merit_label.text = "🕉️ 功德: 0"
	merit_label.position = Vector2(250, 85)
	merit_label.add_theme_font_size_override("font_size", 14)
	merit_label.add_theme_color_override("font_color", Color.LIGHT_CORAL)
	resource_panel.add_child(merit_label)

func create_realm_panel():
	# 境界显示面板 - 纯文字版本
	realm_panel = Panel.new()
	realm_panel.size = Vector2(500, 120)
	realm_panel.position = Vector2(10, 270)
	realm_panel.add_theme_color_override("bg_color", Color(0.2, 0.1, 0.1, 0.9))
	add_child(realm_panel)

	# 境界标题
	var title = Label.new()
	title.text = "═══ 修炼境界 ═══"
	title.position = Vector2(10, 5)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	realm_panel.add_child(title)

	# 当前境界标签
	realm_label = Label.new()
	realm_label.text = "🏔️ 当前境界: 凡人"
	realm_label.position = Vector2(10, 35)
	realm_label.add_theme_font_size_override("font_size", 16)
	realm_label.add_theme_color_override("font_color", Color.LIGHT_CYAN)
	realm_panel.add_child(realm_label)

	# 修炼进度条（文字版）
	progress_bar = ProgressBar.new()
	progress_bar.size = Vector2(400, 25)
	progress_bar.position = Vector2(10, 65)
	progress_bar.min_value = 0.0
	progress_bar.max_value = 1.0
	progress_bar.value = 0.0
	progress_bar.add_theme_color_override("fill", Color.GOLD)
	progress_bar.add_theme_color_override("background", Color(0.3, 0.3, 0.3))
	realm_panel.add_child(progress_bar)

	# 进度标签
	var progress_label = Label.new()
	progress_label.text = "⚡ 突破进度"
	progress_label.position = Vector2(420, 65)
	progress_label.add_theme_font_size_override("font_size", 12)
	progress_label.add_theme_color_override("font_color", Color.YELLOW)
	realm_panel.add_child(progress_label)

func create_building_panel():
	# 建筑选择面板 - 纯文字版本
	building_panel = Panel.new()
	building_panel.size = Vector2(350, 500)
	building_panel.position = Vector2(get_viewport().size.x - 360, 10)
	building_panel.add_theme_color_override("bg_color", Color(0.1, 0.2, 0.1, 0.9))
	add_child(building_panel)

	# 面板标题
	var title = Label.new()
	title.text = "═══ 修仙建筑 ═══"
	title.position = Vector2(10, 10)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	building_panel.add_child(title)

	# 建筑按钮将在GameManager连接后创建

func create_building_buttons():
	var y_offset = 40
	var button_height = 100

	# 只显示已解锁的建筑
	var unlocked_buildings = game_manager.get_unlocked_buildings()

	for building_type in unlocked_buildings:
		var building_data = game_manager.building_types[building_type]

		# 获取建筑图标
		var building_icon = get_building_icon(building_type)

		# 创建建筑按钮
		var button = Button.new()
		button.text = "%s %s" % [building_icon, building_data.name]
		button.size = Vector2(320, 60)
		button.position = Vector2(10, y_offset)
		button.add_theme_font_size_override("font_size", 14)

		# 连接按钮点击事件
		button.pressed.connect(_on_building_button_pressed.bind(building_type))

		building_panel.add_child(button)
		building_buttons[building_type] = button

		# 创建成本标签
		var cost_label = Label.new()
		var cost_text = "💰 成本: "
		for resource in building_data.cost:
			var resource_icon = get_resource_icon(resource)
			cost_text += "%s%d " % [resource_icon, building_data.cost[resource]]
		cost_label.text = cost_text
		cost_label.position = Vector2(10, y_offset + 65)
		cost_label.add_theme_font_size_override("font_size", 11)
		cost_label.add_theme_color_override("font_color", Color.LIGHT_YELLOW)
		building_panel.add_child(cost_label)

		# 创建产出标签
		var production_label = Label.new()
		var production_text = "📈 产出: "
		for resource in building_data.production:
			var resource_icon = get_resource_icon(resource)
			var amount = building_data.production[resource]
			var rate = building_data.production_rate
			production_text += "%s%d/%.1fs " % [resource_icon, amount, rate]
		production_label.text = production_text
		production_label.position = Vector2(10, y_offset + 80)
		production_label.add_theme_font_size_override("font_size", 11)
		production_label.add_theme_color_override("font_color", Color.LIGHT_GREEN)
		building_panel.add_child(production_label)

		y_offset += button_height + 5

func recreate_building_buttons():
	# 清除现有按钮
	clear_building_buttons()
	# 重新创建按钮
	create_building_buttons()

func clear_building_buttons():
	# 清除所有建筑按钮和相关UI元素
	for child in building_panel.get_children():
		if child != building_panel.get_child(0):  # 保留标题
			child.queue_free()
	building_buttons.clear()

func get_resource_display_name(resource_key: String) -> String:
	# 将资源键转换为显示名称
	match resource_key:
		"spirit_stones": return "灵石"
		"qi": return "灵气"
		"disciples": return "弟子"
		"cultivation_exp": return "修炼经验"
		"pills": return "丹药"
		"artifacts": return "法器"
		"merit": return "功德"
		_: return resource_key

func get_resource_icon(resource_key: String) -> String:
	# 获取资源图标
	match resource_key:
		"spirit_stones": return "💎"
		"qi": return "🌀"
		"disciples": return "👥"
		"cultivation_exp": return "⭐"
		"pills": return "💊"
		"artifacts": return "⚔️"
		"merit": return "🕉️"
		_: return "❓"

func get_building_icon(building_type: String) -> String:
	# 获取建筑图标
	match building_type:
		"meditation_room": return "🧘"
		"spirit_garden": return "🌿"
		"alchemy_lab": return "⚗️"
		"artifact_forge": return "🔨"
		"disciple_hall": return "🏛️"
		"treasure_vault": return "🏦"
		_: return "🏗️"

func _on_building_button_pressed(building_type: String):
	# 选择建筑类型
	game_manager.set_selected_building_type(building_type)

	# 更新按钮状态
	update_building_button_states(building_type)

	print("选择了建筑：", building_type)

func update_building_button_states(selected_type: String):
	# 更新建筑按钮的视觉状态
	for building_type in building_buttons:
		var button = building_buttons[building_type]
		if building_type == selected_type:
			button.modulate = Color.GREEN
		else:
			button.modulate = Color.WHITE

func _on_resource_changed(_resource_type: String, _amount: int):
	# 更新资源显示
	update_resource_display()

func _on_realm_breakthrough(_new_realm: String):
	# 境界突破时的UI更新
	update_realm_display()
	show_message("恭喜突破到新境界！")
	# 重新创建建筑按钮（可能解锁新建筑）
	recreate_building_buttons()

func _on_cultivation_progress(progress: float):
	# 修炼进度更新
	if progress_bar:
		progress_bar.value = progress

func update_resource_display():
	# 更新所有修仙资源显示
	if spirit_stones_label:
		spirit_stones_label.text = "💎 灵石: %d" % game_manager.get_resource("spirit_stones")

	if qi_label:
		qi_label.text = "🌀 灵气: %d" % game_manager.get_resource("qi")

	if disciples_label:
		disciples_label.text = "👥 弟子: %d" % game_manager.get_resource("disciples")

	if cultivation_exp_label:
		cultivation_exp_label.text = "⭐ 修炼经验: %d" % game_manager.get_resource("cultivation_exp")

	if pills_label:
		pills_label.text = "💊 丹药: %d" % game_manager.get_resource("pills")

	if artifacts_label:
		artifacts_label.text = "⚔️ 法器: %d" % game_manager.get_resource("artifacts")

	if merit_label:
		merit_label.text = "🕉️ 功德: %d" % game_manager.get_resource("merit")

	# 更新境界显示
	update_realm_display()

	# 更新建筑按钮的可用状态
	update_building_button_availability()

func update_realm_display():
	# 更新境界显示
	if realm_label and game_manager:
		var realm_info = game_manager.get_current_realm_info()
		realm_label.text = "当前境界: %s" % realm_info.current.name

		if progress_bar:
			progress_bar.value = realm_info.progress

func update_building_button_availability():
	# 根据资源情况更新建筑按钮的可用性
	for building_type in building_buttons:
		var button = building_buttons[building_type]
		var building_data = game_manager.building_types[building_type]

		if game_manager.can_afford(building_data.cost):
			button.disabled = false
			button.modulate.a = 1.0
		else:
			button.disabled = true
			button.modulate.a = 0.5

func show_message(message: String):
	# 显示临时消息
	var message_label = Label.new()
	message_label.text = message
	message_label.position = Vector2(get_viewport().size.x / 2 - 100, 100)
	message_label.add_theme_color_override("font_color", Color.YELLOW)
	add_child(message_label)

	# 3秒后移除消息
	var timer = Timer.new()
	timer.wait_time = 3.0
	timer.one_shot = true
	timer.timeout.connect(func(): message_label.queue_free(); timer.queue_free())
	add_child(timer)
	timer.start()

func _input(event):
	# 处理输入事件
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		# 检查是否在游戏区域点击（不在UI面板上）
		if not is_point_in_ui_panels(event.position):
			handle_game_area_click(event.position)

func is_point_in_ui_panels(point: Vector2) -> bool:
	# 检查点击位置是否在UI面板内
	var resource_rect = Rect2(resource_panel.position, resource_panel.size)
	var building_rect = Rect2(building_panel.position, building_panel.size)

	return resource_rect.has_point(point) or building_rect.has_point(point)

func handle_game_area_click(click_position: Vector2):
	# 处理游戏区域的点击
	if game_manager.selected_building_type != "":
		# 尝试在点击位置放置建筑
		var grid_position = snap_to_grid(click_position)

		if game_manager.place_building(game_manager.selected_building_type, grid_position):
			show_message("建筑放置成功！")
		else:
			show_message("无法放置建筑！")

func snap_to_grid(click_position: Vector2) -> Vector2:
	# 将位置对齐到网格
	var grid_size = 64.0
	var x = int(click_position.x / grid_size) * grid_size + grid_size / 2.0
	var y = int(click_position.y / grid_size) * grid_size + grid_size / 2.0
	return Vector2(x, y)
