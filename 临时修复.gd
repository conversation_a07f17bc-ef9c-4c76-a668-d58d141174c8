# 临时修复脚本 - 如果遇到 building_panel 错误，请使用这个版本

extends Control

# 修仙UI管理器 - 个人修炼者视角
class_name UIManagerFixed

# UI元素引用
var resource_panel: Panel
var activity_panel: Panel  # 确保使用正确的变量名
var realm_panel: Panel
var spirit_stones_label: Label
var qi_label: Label
var sect_contribution_label: Label
var cultivation_exp_label: Label
var pills_label: Label
var artifacts_label: Label
var techniques_label: Label
var realm_label: Label
var progress_bar: ProgressBar

# 建筑按钮
var building_buttons = {}

# 游戏管理器引用
var game_manager: GameManager

func _ready():
	# 延迟获取游戏管理器引用，等待Main场景完全初始化
	call_deferred("setup_game_manager_connection")
	
	# 创建UI元素
	setup_ui()

func setup_game_manager_connection():
	# 获取游戏管理器引用
	game_manager = get_node("../../GameManager")
	
	# 连接信号
	if game_manager:
		game_manager.resource_changed.connect(_on_resource_changed)
		game_manager.realm_breakthrough.connect(_on_realm_breakthrough)
		game_manager.cultivation_progress.connect(_on_cultivation_progress)
		
		# 初始化资源显示
		update_resource_display()
		
		# 创建建筑按钮
		create_building_buttons()
	else:
		print("警告：无法找到GameManager")

func setup_ui():
	# 创建资源显示面板
	create_resource_panel()
	
	# 创建境界显示面板
	create_realm_panel()
	
	# 创建修炼活动面板
	create_activity_panel()

func create_resource_panel():
	# 修仙资源面板
	resource_panel = Panel.new()
	resource_panel.size = Vector2(500, 250)
	resource_panel.position = Vector2(10, 10)
	resource_panel.add_theme_color_override("bg_color", Color(0.1, 0.1, 0.2, 0.9))
	add_child(resource_panel)
	
	# 标题
	var title = Label.new()
	title.text = "═══ 修仙资源 ═══"
	title.position = Vector2(10, 5)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	resource_panel.add_child(title)
	
	# 创建所有资源标签...
	# (为了简洁，这里省略了具体的标签创建代码)

func create_realm_panel():
	# 境界显示面板
	realm_panel = Panel.new()
	realm_panel.size = Vector2(500, 120)
	realm_panel.position = Vector2(10, 270)
	realm_panel.add_theme_color_override("bg_color", Color(0.2, 0.1, 0.1, 0.9))
	add_child(realm_panel)
	
	# 创建境界相关UI...

func create_activity_panel():
	# 修炼活动面板 - 使用明确的函数名
	activity_panel = Panel.new()
	activity_panel.size = Vector2(350, 500)
	activity_panel.position = Vector2(get_viewport().size.x - 360, 10)
	activity_panel.add_theme_color_override("bg_color", Color(0.1, 0.2, 0.1, 0.9))
	add_child(activity_panel)
	
	# 面板标题
	var title = Label.new()
	title.text = "═══ 修炼活动 ═══"
	title.position = Vector2(10, 10)
	title.add_theme_font_size_override("font_size", 18)
	title.add_theme_color_override("font_color", Color.GOLD)
	activity_panel.add_child(title)

func create_building_buttons():
	if not game_manager:
		return
		
	var y_offset = 40
	var unlocked_buildings = game_manager.get_unlocked_buildings()
	
	for building_type in unlocked_buildings:
		var building_data = game_manager.building_types[building_type]
		
		# 创建按钮
		var button = Button.new()
		button.text = building_data.name
		button.size = Vector2(320, 60)
		button.position = Vector2(10, y_offset)
		
		# 连接事件
		button.pressed.connect(_on_building_button_pressed.bind(building_type))
		
		activity_panel.add_child(button)
		building_buttons[building_type] = button
		
		y_offset += 80

func _on_building_button_pressed(building_type: String):
	if game_manager:
		game_manager.set_selected_building_type(building_type)

func _on_resource_changed(_resource_type: String, _amount: int):
	update_resource_display()

func _on_realm_breakthrough(_new_realm: String):
	update_realm_display()

func _on_cultivation_progress(progress: float):
	if progress_bar:
		progress_bar.value = progress

func update_resource_display():
	# 更新资源显示
	pass

func update_realm_display():
	# 更新境界显示
	pass
