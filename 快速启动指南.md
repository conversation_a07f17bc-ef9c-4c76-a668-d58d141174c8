# 修仙放置游戏 - 个人修炼者版快速启动指南

## 🚀 立即开始

### 1. 打开Godot项目
1. 启动Godot引擎（需要4.2+版本）
2. 点击"导入"按钮
3. 选择项目文件夹（包含`project.godot`的文件夹）
4. 点击"导入并编辑"

### 2. 验证主场景
项目已经包含了完整的主场景文件：

1. 确认`scenes/main/Main.tscn`文件存在
2. 主场景已经在项目设置中配置好了
3. 所有必要的脚本都已经正确关联

### 3. 运行游戏
点击播放按钮或按F5运行游戏！

## 🎮 游戏操作

### 基础操作
- **WASD/方向键**: 移动摄像机查看不同区域
- **鼠标滚轮**: 缩放视图
- **左键点击建筑按钮**: 选择要建造的建筑类型
- **左键点击游戏区域**: 在该位置放置选中的建筑

### 界面说明
- **背景**: 🌌 纯黑色背景，营造深邃的修仙氛围
- **左上角**: 📊 修仙资源面板 - 显示个人修炼资源
- **左中**: 🏔️ 修炼境界面板 - 显示当前境界和突破进度
- **右侧**: 🧘 修炼活动面板 - 选择要进行的修炼活动
- **左下角**: 📜 修仙日志 - 记录修炼历程中的重要事件

### 自定义设置
如果您想要显示网格线来帮助建筑放置：
1. 打开 `scripts/main/Main.gd`
2. 找到第17行：`var show_grid_lines = false`
3. 改为：`var show_grid_lines = true`
4. 重新运行游戏即可看到网格线

### 游戏目标
1. **开始修炼**: 选择修炼之地获得灵气和修炼经验
2. **提升境界**: 积累修炼经验自动突破到更高境界
3. **解锁活动**: 每个新境界都会解锁新的修炼活动
4. **资源管理**: 合理分配灵石、灵气等修炼资源
5. **成为大能**: 从普通弟子成长为修仙界的强者

## 📊 初始状态

### 起始资源
- 灵石: 10（基础货币，初始很少）
- 灵气: 100（修炼根本）
- 其他资源: 0

### 起始境界
- 当前境界: 凡人
- 可进行活动: 修炼之地

### 第一步建议
1. 选择"修炼之地"开始基础修炼，消耗灵气获得修炼经验
2. 当修炼经验达到100时，会自动突破到"初学者"境界
3. 解锁采药点后，可以采集灵草获得丹药和门派贡献
4. 继续积累修炼经验，逐步解锁更高级修炼活动

## 🧘 修炼活动解锁顺序

1. **凡人境界**: 修炼之地
2. **初学者境界**: 采药点
3. **修行者境界**: 门派任务
4. **炼体期**: 功法研习
5. **炼气期**: 炼器修行
6. **筑基期**: 内丹修炼

## 💡 游戏技巧

### 资源平衡
- 优先进行修炼之地活动获得修炼经验
- 采药点可以产生丹药和门派贡献
- 门派任务产生灵石，是重要的经济来源

### 境界突破
- 修炼经验是唯一提升境界的方式
- 修炼之地和门派任务都能产生修炼经验
- 境界越高，解锁的修炼活动越强大

### 修炼策略
- 早期专注于修炼之地和采药点
- 中期进行门派任务获得经济优势
- 后期解锁高级修炼活动获得更多资源

## 🐛 常见问题

**Q: 点击建筑按钮没有反应？**
A: 确保已正确设置主场景，并且所有脚本都已正确附加。

**Q: 建筑放置后看不到？**
A: 建筑会显示为彩色方块，检查是否在正确的位置。

**Q: 境界不会突破？**
A: 确保有足够的修炼经验，可以在资源面板查看当前经验值。

**Q: 新建筑没有解锁？**
A: 检查当前境界是否达到建筑的解锁要求。

## 🎉 享受修仙之旅！

现在您可以开始您的修仙放置游戏之旅了！从一个凡人开始，通过建造各种修仙建筑，积累资源，提升境界，最终成为创世境界的至尊存在！

祝您修仙愉快！🌟
