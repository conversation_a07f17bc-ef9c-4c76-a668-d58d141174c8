# 修仙放置游戏 - 纯文字版快速启动指南

## 🚀 立即开始

### 1. 打开Godot项目
1. 启动Godot引擎（需要4.2+版本）
2. 点击"导入"按钮
3. 选择项目文件夹（包含`project.godot`的文件夹）
4. 点击"导入并编辑"

### 2. 验证主场景
项目已经包含了完整的主场景文件：

1. 确认`scenes/main/Main.tscn`文件存在
2. 主场景已经在项目设置中配置好了
3. 所有必要的脚本都已经正确关联

### 3. 运行游戏
点击播放按钮或按F5运行游戏！

## 🎮 游戏操作

### 基础操作
- **WASD/方向键**: 移动摄像机查看不同区域
- **鼠标滚轮**: 缩放视图
- **左键点击建筑按钮**: 选择要建造的建筑类型
- **左键点击游戏区域**: 在该位置放置选中的建筑

### 界面说明
- **背景**: 🌌 纯黑色背景，营造深邃的修仙氛围
- **左上角**: 📊 修仙资源面板 - 显示所有资源数量
- **左中**: 🏔️ 修炼境界面板 - 显示当前境界和突破进度
- **右侧**: 🏗️ 修仙建筑面板 - 选择要建造的建筑
- **左下角**: 📜 修仙日志 - 记录游戏中的重要事件

### 自定义设置
如果您想要显示网格线来帮助建筑放置：
1. 打开 `scripts/main/Main.gd`
2. 找到第17行：`var show_grid_lines = false`
3. 改为：`var show_grid_lines = true`
4. 重新运行游戏即可看到网格线

### 游戏目标
1. **开始修炼**: 建造静修室获得灵气和修炼经验
2. **提升境界**: 积累修炼经验自动突破到更高境界
3. **解锁建筑**: 每个新境界都会解锁新的建筑类型
4. **资源管理**: 合理分配灵石、灵气等资源
5. **建设门派**: 逐步建造完整的修仙门派

## 📊 初始状态

### 起始资源
- 灵石: 100（基础货币）
- 灵气: 50（修炼资源）
- 其他资源: 0

### 起始境界
- 当前境界: 凡人
- 可建造建筑: 静修室

### 第一步建议
1. 建造2-3个静修室开始产生灵气和修炼经验
2. 当修炼经验达到100时，会自动突破到"初学者"境界
3. 解锁灵药园后，建造灵药园产生丹药
4. 继续积累修炼经验，逐步解锁更高级建筑

## 🏛️ 建筑解锁顺序

1. **凡人境界**: 静修室
2. **初学者境界**: 灵药园
3. **修行者境界**: 炼丹房
4. **炼体期**: 炼器坊
5. **炼气期**: 弟子堂
6. **筑基期**: 宝库

## 💡 游戏技巧

### 资源平衡
- 优先建造静修室获得修炼经验
- 灵药园可以产生丹药，用于建造炼丹房
- 炼丹房产生大量灵石，是重要的经济来源

### 境界突破
- 修炼经验是唯一提升境界的方式
- 静修室和炼丹房都能产生修炼经验
- 境界越高，解锁的建筑越强大

### 建筑策略
- 早期专注于静修室和灵药园
- 中期建造炼丹房获得经济优势
- 后期解锁高级建筑获得更多资源

## 🐛 常见问题

**Q: 点击建筑按钮没有反应？**
A: 确保已正确设置主场景，并且所有脚本都已正确附加。

**Q: 建筑放置后看不到？**
A: 建筑会显示为彩色方块，检查是否在正确的位置。

**Q: 境界不会突破？**
A: 确保有足够的修炼经验，可以在资源面板查看当前经验值。

**Q: 新建筑没有解锁？**
A: 检查当前境界是否达到建筑的解锁要求。

## 🎉 享受修仙之旅！

现在您可以开始您的修仙放置游戏之旅了！从一个凡人开始，通过建造各种修仙建筑，积累资源，提升境界，最终成为创世境界的至尊存在！

祝您修仙愉快！🌟
