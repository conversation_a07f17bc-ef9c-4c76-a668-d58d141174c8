[gd_scene load_steps=3 format=3 uid="uid://bkxvn8qyqpwxe"]

[ext_resource type="Script" uid="uid://bxamhr14fyba8" path="res://scripts/main/Main.gd" id="1_main_script"]
[ext_resource type="Script" uid="uid://bslq12cfjceln" path="res://scripts/ui/UIManager.gd" id="2_ui_script"]

[node name="Main" type="Node2D"]
script = ExtResource("1_main_script")

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(640, 360)

[node name="Background" type="ColorRect" parent="."]
z_index = -100
offset_right = 2000.0
offset_bottom = 1500.0
color = Color(0, 0, 0, 1)

[node name="UILayer" type="CanvasLayer" parent="."]

[node name="UIManager" type="Control" parent="UILayer"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("2_ui_script")

[node name="GameLog" type="Panel" parent="UILayer"]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -200.0
offset_right = 500.0
offset_bottom = -10.0

[node name="LogTitle" type="Label" parent="UILayer/GameLog"]
layout_mode = 0
anchor_left = 1.0
anchor_right = 1.0
offset_left = -480.0
offset_top = 5.0
offset_right = -10.0
offset_bottom = 25.0
text = "═══ 修仙日志 ═══"
horizontal_alignment = 1

[node name="LogText" type="RichTextLabel" parent="UILayer/GameLog"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 30.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2
bbcode_enabled = true
text = "[color=cyan]欢迎来到修仙世界！[/color]
[color=yellow]开始您的修仙之旅吧...[/color]"
scroll_following = true
